import { Settings, CropBox } from '@/types';

export interface OCRArgs {
  video_path: string;
  output: string;
  lang: string;
  subtitle_position: string;
  time_start?: string;
  time_end?: string;
  conf_threshold: number;
  sim_threshold: number;
  max_merge_gap: number;
  brightness_threshold?: number;
  ssim_threshold: number;
  ocr_image_max_width: number;
  frames_to_skip: number;
  min_subtitle_duration: number;
  use_fullframe: boolean;
  use_gpu: boolean;
  use_angle_cls: boolean;
  post_processing: boolean;
  use_server_model: boolean;
  use_dual_zone: boolean;
  send_notification: boolean;
  crop_x?: number;
  crop_y?: number;
  crop_width?: number;
  crop_height?: number;
  crop_x2?: number;
  crop_y2?: number;
  crop_width2?: number;
  crop_height2?: number;
}

// CLI argument mappings for different versions
const CLI_ARG_MAPPINGS = {
  // Standard videocr-cli arguments (based on actual CLI help)
  standard: {
    subtitle_position: 'subtitle_position',
    time_start: 'time_start',
    time_end: 'time_end',
    conf_threshold: 'conf_threshold',
    sim_threshold: 'sim_threshold',
    max_merge_gap: 'max_merge_gap',
    brightness_threshold: 'brightness_threshold',
    ssim_threshold: 'ssim_threshold',
    ocr_image_max_width: null, // Not supported by this CLI version
    frames_to_skip: 'frames_to_skip',
    min_subtitle_duration: 'min_subtitle_duration',
    use_fullframe: 'use_fullframe',
    use_gpu: 'use_gpu',
    use_angle_cls: 'use_angle_cls',
    post_processing: 'post_processing',
    use_server_model: 'use_server_model',
    use_dual_zone: 'use_dual_zone',
    send_notification: 'send_notification'
  },
  // videocr-cli-sa arguments (simplified version)
  simplified: {
    subtitle_position: null, // Not supported
    time_start: 'time_start',
    time_end: 'time_end',
    conf_threshold: 'conf_threshold',
    sim_threshold: 'sim_threshold',
    max_merge_gap: null, // Not supported
    brightness_threshold: null, // Not supported
    ssim_threshold: 'similar_image_threshold',
    ocr_image_max_width: null, // Not supported
    frames_to_skip: 'frames_to_skip',
    min_subtitle_duration: null, // Not supported
    use_fullframe: 'use_fullframe',
    use_gpu: 'use_gpu',
    use_angle_cls: null, // Not supported
    post_processing: null, // Not supported
    use_server_model: null, // Not supported
    use_dual_zone: null, // Not supported
    send_notification: null // Not supported
  }
};

export class OCRService {
  static detectCLIVersion(cliPath: string): 'standard' | 'simplified' {
    // Detect based on filename
    if (cliPath.includes('videocr-cli-sa')) {
      return 'simplified';
    }
    return 'standard';
  }

  static validateSettings(settings: Settings): string[] {
    const errors: string[] = [];

    // Time format validation
    const timeRegex = /^(\d{1,2}:\d{2}|\d{1,2}:\d{2}:\d{2})$/;
    
    if (settings.timeStart && !timeRegex.test(settings.timeStart)) {
      errors.push('Invalid start time format. Use MM:SS or HH:MM:SS.');
    }
    
    if (settings.timeEnd && !timeRegex.test(settings.timeEnd)) {
      errors.push('Invalid end time format. Use MM:SS or HH:MM:SS.');
    }

    // Numeric validations
    const numericValidations = [
      { value: settings.confThreshold, min: 0, max: 100, name: 'Confidence Threshold' },
      { value: settings.simThreshold, min: 0, max: 100, name: 'Similarity Threshold' },
      { value: settings.ssimThreshold, min: 0, max: 100, name: 'SSIM Threshold' },
      { value: settings.maxMergeGap, min: 0, max: null, name: 'Max Merge Gap' },
      { value: settings.minSubtitleDuration, min: 0, max: null, name: 'Min Subtitle Duration' },
      // { value: settings.ocrImageMaxWidth, min: 0, max: null, name: 'OCR Image Max Width' },
      { value: settings.framesToSkip, min: 0, max: null, name: 'Frames to Skip' },
      { value: settings.keyboardSeekStep, min: 1, max: null, name: 'Keyboard Seek Step' }
    ];

    if (settings.brightnessThreshold !== undefined) {
      numericValidations.push({
        value: settings.brightnessThreshold,
        min: 0,
        max: 255,
        name: 'Brightness Threshold'
      });
    }

    for (const validation of numericValidations) {
      if (validation.value < validation.min) {
        errors.push(`${validation.name} must be at least ${validation.min}`);
      }
      if (validation.max !== null && validation.value > validation.max) {
        errors.push(`${validation.name} must be at most ${validation.max}`);
      }
    }

    return errors;
  }

  static validateTimeRange(
    startTime: string,
    endTime: string,
    videoDuration: number
  ): string[] {
    const errors: string[] = [];

    const parseTime = (timeStr: string): number | null => {
      if (!timeStr) return null;
      
      const parts = timeStr.split(':').map(Number);
      if (parts.length === 2) {
        return parts[0] * 60 + parts[1];
      } else if (parts.length === 3) {
        return parts[0] * 3600 + parts[1] * 60 + parts[2];
      }
      return null;
    };

    const startSeconds = parseTime(startTime);
    const endSeconds = parseTime(endTime);

    if (startSeconds !== null && startSeconds > videoDuration) {
      errors.push(`Start time exceeds video duration (${this.formatTime(videoDuration)})`);
    }

    if (endSeconds !== null && endSeconds > videoDuration) {
      errors.push(`End time exceeds video duration (${this.formatTime(videoDuration)})`);
    }

    if (startSeconds !== null && endSeconds !== null && startSeconds >= endSeconds) {
      errors.push('Start time must be before end time');
    }

    return errors;
  }

  static formatTime(seconds: number): string {
    const h = Math.floor(seconds / 3600);
    const m = Math.floor((seconds % 3600) / 60);
    const s = Math.floor(seconds % 60);
    
    if (h > 0) {
      return `${h.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}:${s.toString().padStart(2, '0')}`;
    }
    return `${m.toString().padStart(2, '0')}:${s.toString().padStart(2, '0')}`;
  }

  static buildOCRArgs(
    videoPath: string,
    outputPath: string,
    settings: Settings,
    cropBoxes: CropBox[]
  ): any {
    // Detect CLI version
    const cliPath = settings.customVideOCRPath || '';
    const cliVersion = this.detectCLIVersion(cliPath);

    // Start with basic required arguments
    const args: any = {
      video_path: videoPath,
      output: outputPath,
      lang: settings.language || 'chinese_cht'
    };

    // Convert camelCase settings to CLI arguments with underscores
    const settingsMapping: Record<string, string> = {
      subtitlePosition: 'subtitle_position',
      timeStart: 'time_start',
      timeEnd: 'time_end',
      confThreshold: 'conf_threshold',
      simThreshold: 'sim_threshold',
      maxMergeGap: 'max_merge_gap',
      brightnessThreshold: 'brightness_threshold',
      ssimThreshold: 'ssim_threshold',
      ocrImageMaxWidth: 'ocr_image_max_width',
      framesToSkip: 'frames_to_skip',
      minSubtitleDuration: 'min_subtitle_duration',
      useFullframe: 'use_fullframe',
      useGpu: 'use_gpu',
      useAngleCls: 'use_angle_cls',
      postProcessing: 'post_processing',
      useServerModel: 'use_server_model',
      useDualZone: 'use_dual_zone',
      sendNotification: 'send_notification'
    };

    // Apply settings based on CLI version compatibility
    for (const [camelKey, underscoreKey] of Object.entries(settingsMapping)) {
      const value = (settings as any)[camelKey];

      // Skip undefined, null, or empty string values
      if (value === undefined || value === null || value === '') {
        continue;
      }

      // Check CLI version compatibility
      const mappings = CLI_ARG_MAPPINGS[cliVersion];
      if (mappings && mappings[underscoreKey as keyof typeof mappings] === null) {
        console.log(`⚠️ Skipping ${underscoreKey} - not supported by ${cliVersion} CLI`);
        continue;
      }

      // Use mapped name if available, otherwise use the underscore key
      const finalKey = mappings && mappings[underscoreKey as keyof typeof mappings]
        ? mappings[underscoreKey as keyof typeof mappings]
        : underscoreKey;

      if (finalKey) {
        args[finalKey] = value;
      }
    }

    // Add crop box parameters if available
    if (cropBoxes && cropBoxes.length > 0) {
      const primaryCrop = cropBoxes[0];
      if (primaryCrop) {
        args.crop_x = Math.round(primaryCrop.x);
        args.crop_y = Math.round(primaryCrop.y);
        args.crop_width = Math.round(primaryCrop.width);
        args.crop_height = Math.round(primaryCrop.height);
      }

      // Add secondary crop box for dual zone if enabled and available
      if (settings.useDualZone && cropBoxes.length > 1) {
        const secondaryCrop = cropBoxes[1];
        if (secondaryCrop) {
          args.crop_x2 = Math.round(secondaryCrop.x);
          args.crop_y2 = Math.round(secondaryCrop.y);
          args.crop_width2 = Math.round(secondaryCrop.width);
          args.crop_height2 = Math.round(secondaryCrop.height);
        }
      }
    }

    // Add metadata for main process (will be filtered out before CLI execution)
    args.customVideOCRPath = settings.customVideOCRPath;

    console.log('🚀 Final OCR args:', args);
    return args;
  }

  static async startOCR(args: OCRArgs): Promise<void> {
    return window.electronAPI.ipcRenderer.invoke('ocr:start', args);
  }

  static async cancelOCR(): Promise<boolean> {
    try {
      await window.electronAPI.ipcRenderer.invoke('ocr:cancel');
      return true;
    } catch (error) {
      console.error('Cancel OCR error:', error);
      return false;
    }
  }

  static generateOutputPath(videoPath: string, saveInVideoDir: boolean, defaultDir: string): string {
    const videoFile = videoPath.replace(/\\/g, '/');
    const lastSlash = videoFile.lastIndexOf('/');
    const fileName = videoFile.substring(lastSlash + 1);
    const baseName = fileName.replace(/\.[^/.]+$/, '');
    
    if (saveInVideoDir) {
      const videoDir = videoFile.substring(0, lastSlash);
      return `${videoDir}/${baseName}.srt`;
    } else {
      return `${defaultDir}/${baseName}.srt`;
    }
  }

  static parseProgressFromOutput(output: string): {
    step: string;
    current: number;
    total: number;
    percentage: number;
  } | null {
    // Progress patterns from the original Python code
    const patterns = [
      {
        regex: /Step 2: Performing OCR on image (\d+) of (\d+)/,
        step: 'Step 2: OCR Processing'
      },
      {
        regex: /Step 1: Processing image (\d+) of (\d+)/,
        step: 'Step 1: Image Processing'
      },
      {
        regex: /Mapping frame (\d+) of (\d+)/,
        step: 'Building Timestamp Map'
      },
      {
        regex: /Advancing to frame (\d+)\/(\d+)/,
        step: 'Seeking to Start Time'
      }
    ];

    for (const pattern of patterns) {
      const match = pattern.regex.exec(output);
      if (match) {
        const current = parseInt(match[1]);
        const total = parseInt(match[2]);
        const percentage = Math.round((current / total) * 100);
        
        return {
          step: pattern.step,
          current,
          total,
          percentage
        };
      }
    }

    return null;
  }
}
