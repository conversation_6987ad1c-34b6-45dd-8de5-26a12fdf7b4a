import React, { useRef, useEffect } from 'react';
import { Button, Space, Typography } from 'antd';

const { Text } = Typography;

interface VideoTestProps {
  videoPath: string;
}

const VideoTest: React.FC<VideoTestProps> = ({ videoPath }) => {
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    if (!videoPath || !videoRef.current) return;

    const video = videoRef.current;
    
    // Test different URL formats
    const testUrls = [
      `file:///${videoPath.replace(/\\/g, '/')}`,
      `file://${videoPath.replace(/\\/g, '/')}`,
      videoPath,
      videoPath.replace(/\\/g, '/')
    ];

    console.log('🧪 Testing video URLs:');
    testUrls.forEach((url, index) => {
      console.log(`${index + 1}. ${url}`);
    });

    let currentTest = 0;

    const testNextUrl = () => {
      if (currentTest >= testUrls.length) {
        console.log('❌ All URL formats failed');
        return;
      }

      const url = testUrls[currentTest];
      console.log(`🧪 Testing URL ${currentTest + 1}: ${url}`);
      
      video.src = url;
      video.load();
      currentTest++;
    };

    const handleLoadedMetadata = () => {
      console.log(`✅ SUCCESS with URL: ${video.src}`);
      console.log(`📐 Dimensions: ${video.videoWidth}x${video.videoHeight}`);
      console.log(`⏱️ Duration: ${video.duration}s`);
    };

    const handleError = () => {
      console.log(`❌ FAILED with URL: ${video.src}`);
      console.log(`Error:`, video.error);
      setTimeout(testNextUrl, 100);
    };

    video.addEventListener('loadedmetadata', handleLoadedMetadata);
    video.addEventListener('error', handleError);

    testNextUrl();

    return () => {
      video.removeEventListener('loadedmetadata', handleLoadedMetadata);
      video.removeEventListener('error', handleError);
    };
  }, [videoPath]);

  return (
    <div style={{ padding: '20px', border: '1px solid #434343' }}>
      <Text strong>Video Test Component</Text>
      <br />
      <Text type="secondary">Path: {videoPath}</Text>
      
      <video
        ref={videoRef}
        controls
        style={{ 
          width: '100%', 
          maxWidth: '640px', 
          height: 'auto',
          marginTop: '10px',
          backgroundColor: '#000'
        }}
      />
      
      <div style={{ marginTop: '10px' }}>
        <Space>
          <Button onClick={() => {
            if (videoRef.current) {
              videoRef.current.currentTime = 0;
              videoRef.current.play();
            }
          }}>
            Play
          </Button>
          
          <Button onClick={() => {
            if (videoRef.current) {
              videoRef.current.pause();
            }
          }}>
            Pause
          </Button>
          
          <Button onClick={() => {
            if (videoRef.current) {
              console.log('Video info:', {
                src: videoRef.current.src,
                readyState: videoRef.current.readyState,
                networkState: videoRef.current.networkState,
                error: videoRef.current.error
              });
            }
          }}>
            Debug Info
          </Button>
        </Space>
      </div>
    </div>
  );
};

export default VideoTest;
