import React, { useEffect } from "react";
import { ConfigProvider, Layout, Tabs, App as AntdApp } from "antd";
import {
  VideoCameraOutlined,
  SettingOutlined,
  InfoCircleOutlined
} from "@ant-design/icons";
import { useAppStore } from "@/stores/useAppStore";
import VideoTab from "@/components/VideoTab";
import SettingsTab from "@/components/SettingsTab";
import AboutTab from "@/components/AboutTab";

const { Content } = Layout;

const App: React.FC = () => {
  const { activeTab, setActiveTab, loadSettings, cleanup } = useAppStore();

  useEffect(() => {
    // Load settings on app start
    loadSettings();

    // Set up IPC listeners for OCR events with throttling
    let progressBuffer: string[] = [];
    let progressTimeout: NodeJS.Timeout | null = null;

    const handleOCRProgress = (event: any, data: string) => {
      // Buffer progress messages to reduce UI updates
      progressBuffer.push(data);

      if (progressTimeout) {
        clearTimeout(progressTimeout);
      }

      progressTimeout = setTimeout(() => {
        if (progressBuffer.length > 0) {
          // Only add the latest progress message to avoid spam
          const latestMessage = progressBuffer[progressBuffer.length - 1];
          useAppStore.getState().addOutputLog(latestMessage);
          progressBuffer = [];
        }
        progressTimeout = null;
      }, 100); // Update every 100ms max
    };

    const handleOCRComplete = (
      event: any,
      result: { success: boolean; output: string; code?: number }
    ) => {
      const { setProcessStatus, addOutputLog } = useAppStore.getState();

      setProcessStatus({
        isRunning: false,
        canCancel: false
      });

      if (result.success) {
        addOutputLog("\nSuccessfully generated subtitle file!\n");
      } else {
        addOutputLog(`\nProcess finished with exit code: ${result.code}\n`);
      }
    };

    const handleOCRError = (event: any, error: string) => {
      const { setProcessStatus, addOutputLog } = useAppStore.getState();

      setProcessStatus({
        isRunning: false,
        canCancel: false
      });

      addOutputLog(`\nError: ${error}\n`);
    };

    // Register IPC listeners
    window.electronAPI.ipcRenderer.on("ocr:progress", handleOCRProgress);
    window.electronAPI.ipcRenderer.on("ocr:complete", handleOCRComplete);
    window.electronAPI.ipcRenderer.on("ocr:error", handleOCRError);

    // Cleanup listeners on unmount
    return () => {
      if (progressTimeout) {
        clearTimeout(progressTimeout);
      }
      window.electronAPI.ipcRenderer.removeAllListeners("ocr:progress");
      window.electronAPI.ipcRenderer.removeAllListeners("ocr:complete");
      window.electronAPI.ipcRenderer.removeAllListeners("ocr:error");
      cleanup();
    };
  }, [loadSettings, cleanup]);

  const tabItems = [
    {
      key: "video",
      label: (
        <span>
          <VideoCameraOutlined /> Process Video
        </span>
      ),
      children: <VideoTab />
    },
    {
      key: "settings",
      label: (
        <span>
          <SettingOutlined /> Advanced Settings
        </span>
      ),
      children: <SettingsTab />
    },
    {
      key: "about",
      label: (
        <span>
          <InfoCircleOutlined /> About
        </span>
      ),
      children: <AboutTab />
    }
  ];
  const themeConfig = {
    token: {
      colorPrimary: "#1890ff",
      colorBgBase: "#141414",
      colorBgContainer: "#1f1f1f",
      colorBorder: "#434343",
      colorText: "#ffffff",
      colorTextSecondary: "#a6a6a6",
      borderRadius: 6,
      fontSize: 14
    },
    components: {
      Layout: {
        bodyBg: "#141414",
        headerBg: "#1f1f1f",
        siderBg: "#1f1f1f"
      },
      Menu: {
        darkItemBg: "#1f1f1f",
        darkSubMenuItemBg: "#1f1f1f"
      },
      Tabs: {
        cardBg: "#1f1f1f"
      },
      Input: {
        colorBgContainer: "#262626"
      },
      Select: {
        colorBgContainer: "#262626"
      },
      Button: {
        colorBgContainer: "#262626"
      }
    }
  };
  return (
    <ConfigProvider theme={themeConfig}>
      <AntdApp>
        <Layout className="app-layout">
          <Content className="content-area">
            <Tabs
              activeKey={activeTab}
              onChange={setActiveTab}
              items={tabItems}
              size="large"
              type="card"
              style={{ height: "100%" }}
            />
          </Content>
        </Layout>
      </AntdApp>
    </ConfigProvider>
  );
};

export default App;
