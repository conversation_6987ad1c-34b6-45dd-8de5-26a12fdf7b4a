import { Language, SubtitlePosition } from '@/types';

export const PROGRAM_VERSION = '1.3.1';

export const DEFAULT_SETTINGS = {
  // OCR Settings
  language: 'chinese_cht',
  subtitlePosition: 'center',
  timeStart: '0:00',
  timeEnd: '',
  confThreshold: 75,
  simThreshold: 80,
  maxMergeGap: 0.09,
  brightnessThreshold: undefined,
  ssimThreshold: 92,
  ocrImageMaxWidth: 1280,
  framesToSkip: 1,
  minSubtitleDuration: 0.2,
  useFullframe: false,
  useGpu: true,
  useAngleCls: false,
  postProcessing: true,
  useServerModel: false,
  useDualZone: false,
  
  // App Settings
  keyboardSeekStep: 1,
  defaultOutputDir: '',
  saveInVideoDir: true,
  sendNotification: true,
  saveCropBox: true,
  savedCropBoxes: [],
  customVideOCRPath: '' // Empty means use default bundled CLI
};

export const LANGUAGES: Language[] = [
  { name: '<PERSON><PERSON><PERSON>', code: 'abq' },
  { name: '<PERSON><PERSON><PERSON><PERSON>', code: 'ady' },
  { name: 'Afrikaans', code: 'af' },
  { name: 'Albanian', code: 'sq' },
  { name: 'Angika', code: 'ang' },
  { name: 'Arabic', code: 'ar' },
  { name: 'Avar', code: 'ava' },
  { name: 'Azerbaijani', code: 'az' },
  { name: 'Belarusian', code: 'be' },
  { name: 'Bhojpuri', code: 'bho' },
  { name: 'Bihari', code: 'bh' },
  { name: 'Bosnian', code: 'bs' },
  { name: 'Bulgarian', code: 'bg' },
  { name: 'Chechen', code: 'che' },
  { name: 'Chinese & English', code: 'ch' },
  { name: 'Chinese Traditional', code: 'chinese_cht' },
  { name: 'Croatian', code: 'hr' },
  { name: 'Czech', code: 'cs' },
  { name: 'Danish', code: 'da' },
  { name: 'Dargwa', code: 'dar' },
  { name: 'Dutch', code: 'nl' },
  { name: 'English', code: 'en' },
  { name: 'Estonian', code: 'et' },
  { name: 'French', code: 'fr' },
  { name: 'German', code: 'german' },
  { name: 'Goan Konkani', code: 'gom' },
  { name: 'Haryanvi', code: 'bgc' },
  { name: 'Hindi', code: 'hi' },
  { name: 'Hungarian', code: 'hu' },
  { name: 'Icelandic', code: 'is' },
  { name: 'Indonesian', code: 'id' },
  { name: 'Ingush', code: 'inh' },
  { name: 'Irish', code: 'ga' },
  { name: 'Italian', code: 'it' },
  { name: 'Japanese', code: 'japan' },
  { name: 'Kabardian', code: 'kbd' },
  { name: 'Korean', code: 'korean' },
  { name: 'Kurdish', code: 'ku' },
  { name: 'Lak', code: 'lbe' },
  { name: 'Latin', code: 'la' },
  { name: 'Latvian', code: 'lv' },
  { name: 'Lezghian', code: 'lez' },
  { name: 'Lithuanian', code: 'lt' },
  { name: 'Magahi', code: 'mah' },
  { name: 'Maithili', code: 'mai' },
  { name: 'Malay', code: 'ms' },
  { name: 'Maltese', code: 'mt' },
  { name: 'Maori', code: 'mi' },
  { name: 'Marathi', code: 'mr' },
  { name: 'Mongolian', code: 'mn' },
  { name: 'Nagpur', code: 'sck' },
  { name: 'Nepali', code: 'ne' },
  { name: 'Newari', code: 'new' },
  { name: 'Norwegian', code: 'no' },
  { name: 'Occitan', code: 'oc' },
  { name: 'Pali', code: 'pi' },
  { name: 'Persian', code: 'fa' },
  { name: 'Polish', code: 'pl' },
  { name: 'Portuguese', code: 'pt' },
  { name: 'Romanian', code: 'ro' },
  { name: 'Russian', code: 'ru' },
  { name: 'Sanskrit', code: 'sa' },
  { name: 'Serbian(cyrillic)', code: 'rs_cyrillic' },
  { name: 'Serbian(latin)', code: 'rs_latin' },
  { name: 'Slovak', code: 'sk' },
  { name: 'Slovenian', code: 'sl' },
  { name: 'Spanish', code: 'es' },
  { name: 'Swahili', code: 'sw' },
  { name: 'Swedish', code: 'sv' },
  { name: 'Tabassaran', code: 'tab' },
  { name: 'Tagalog', code: 'tl' },
  { name: 'Tamil', code: 'ta' },
  { name: 'Telugu', code: 'te' },
  { name: 'Turkish', code: 'tr' },
  { name: 'Ukrainian', code: 'uk' },
  { name: 'Urdu', code: 'ur' },
  { name: 'Uyghur', code: 'ug' },
  { name: 'Uzbek', code: 'uz' },
  { name: 'Vietnamese', code: 'vi' },
  { name: 'Welsh', code: 'cy' }
].sort((a, b) => a.name.localeCompare(b.name));

export const SUBTITLE_POSITIONS: SubtitlePosition[] = [
  { name: 'Center', value: 'center' },
  { name: 'Left', value: 'left' },
  { name: 'Right', value: 'right' },
  { name: 'Any', value: 'any' }
];

export const VIDEO_EXTENSIONS = [
  '.mp4', '.avi', '.mkv', '.mov', '.webm', '.flv', '.wmv', '.ts', '.m2ts'
];

export const GRAPH_SIZE = {
  width: 640,
  height: 360
};
