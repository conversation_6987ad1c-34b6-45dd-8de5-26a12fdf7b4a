import React from 'react';
import { Card, Typography, Space, Button, Divider } from 'antd';
import { GithubOutlined, BugOutlined } from '@ant-design/icons';
import { PROGRAM_VERSION } from '@/constants';

const { Title, Text, Link } = Typography;

const AboutTab: React.FC = () => {
  const handleOpenGitHub = () => {
    window.electronAPI.ipcRenderer.invoke('shell:openExternal', 'https://github.com/tangfeng2/VideOCR');
  };

  const handleOpenIssues = () => {
    window.electronAPI.ipcRenderer.invoke('shell:openExternal', 'https://github.com/tangfeng2/VideOCR/issues');
  };

  const handleOpenReleases = () => {
    window.electronAPI.ipcRenderer.invoke('shell:openExternal', 'https://github.com/tangfeng2/VideOCR/releases');
  };

  return (
    <div style={{ 
      padding: '32px', 
      display: 'flex', 
      justifyContent: 'center',
      alignItems: 'center',
      minHeight: '60vh'
    }}>
      <Card 
        style={{ 
          maxWidth: '600px', 
          width: '100%',
          textAlign: 'center'
        }}
      >
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          {/* App Info */}
          <div>
            <Title level={1} style={{ marginBottom: '8px' }}>
              VideOCR
            </Title>
            <Text type="secondary" style={{ fontSize: '16px' }}>
              Version {PROGRAM_VERSION}
            </Text>
          </div>

          <Text style={{ fontSize: '16px', color: '#a6a6a6' }}>
            Extract subtitles from videos using OCR technology
          </Text>

          <Divider />

          {/* Features */}
          <div style={{ textAlign: 'left' }}>
            <Title level={4}>Features</Title>
            <ul style={{ color: '#a6a6a6', lineHeight: '1.8' }}>
              <li>Extract subtitles from any video format</li>
              <li>Support for 60+ languages</li>
              <li>Interactive crop box selection</li>
              <li>Dual-zone OCR processing</li>
              <li>GPU acceleration support</li>
              <li>Advanced OCR settings and filters</li>
              <li>Real-time progress tracking</li>
              <li>Cross-platform compatibility</li>
            </ul>
          </div>

          <Divider />

          {/* Links */}
          <div>
            <Title level={4}>Get Help & Updates</Title>
            <Space direction="vertical" size="middle">
              <Button
                type="primary"
                icon={<GithubOutlined />}
                onClick={handleOpenReleases}
                size="large"
              >
                Download Latest Version
              </Button>
              
              <Space>
                <Button
                  icon={<GithubOutlined />}
                  onClick={handleOpenGitHub}
                >
                  View Source Code
                </Button>
                
                <Button
                  icon={<BugOutlined />}
                  onClick={handleOpenIssues}
                >
                  Report Issues
                </Button>
              </Space>
            </Space>
          </div>

          <Divider />

          {/* Credits */}
          <div>
            <Text type="secondary">
              Developed by <Text strong>tangfeng2</Text>
            </Text>
            <br />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              Built with Electron, React, and Ant Design
            </Text>
          </div>

          {/* System Info */}
          <div style={{ textAlign: 'left', backgroundColor: '#1f1f1f', padding: '12px', borderRadius: '6px' }}>
            <Title level={5}>System Information</Title>
            <Text type="secondary" style={{ fontSize: '12px', fontFamily: 'monospace' }}>
              Platform: {window.electronAPI.platform}<br />
              Node.js: {window.electronAPI.versions.node}<br />
              Chromium: {window.electronAPI.versions.chrome}<br />
              Electron: {window.electronAPI.versions.electron}
            </Text>
          </div>
        </Space>
      </Card>
    </div>
  );
};

export default AboutTab;
