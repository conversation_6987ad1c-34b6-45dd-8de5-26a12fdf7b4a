import { app, BrowserWindow, ipcMain, dialog, shell, Menu, nativeTheme, Notification } from 'electron';
import { join } from 'path';
import { spawn, ChildProcess } from 'child_process';
import { existsSync, readFileSync, writeFileSync, mkdirSync } from 'fs';
import { homedir } from 'os';

const isDev = process.env.NODE_ENV === 'development';
const isWin = process.platform === 'win32';

let mainWindow: BrowserWindow | null = null;
let ocrProcess: ChildProcess | null = null;

// App paths
const APP_DATA_PATH = isWin 
  ? join(process.env.LOCALAPPDATA || homedir(), 'VideOCR')
  : join(homedir(), '.config', 'VideOCR');

const CONFIG_FILE = join(APP_DATA_PATH, 'config.json');

// Ensure app data directory exists
if (!existsSync(APP_DATA_PATH)) {
  mkdirSync(APP_DATA_PATH, { recursive: true });
}

// Set app user model ID for Windows
if (isWin) {
  app.setAppUserModelId('VideOCR');
}

// Single instance lock (disabled in development)
if (!isDev) {
  const gotTheLock = app.requestSingleInstanceLock();

  if (!gotTheLock) {
    console.log('Another instance is already running. Exiting...');
    app.quit();
  } else {
    app.on('second-instance', () => {
      if (mainWindow) {
        if (mainWindow.isMinimized()) mainWindow.restore();
        mainWindow.focus();
      }
    });
  }
} else {
  console.log('Development mode: Single instance lock disabled');
}

function createWindow() {
  console.log('📱 Creating main window...');

  const preloadPath = join(__dirname, 'preload.js');
  const iconPath = join(__dirname, '../assets/VideOCR.png');

  console.log('🔧 Preload path:', preloadPath);
  console.log('🎨 Icon path:', iconPath);
  console.log('🔍 Preload exists:', existsSync(preloadPath));
  console.log('🔍 Icon exists:', existsSync(iconPath));

  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: preloadPath,
      webSecurity: false, // Allow file access for video loading
      allowRunningInsecureContent: true
    },
    icon: iconPath,
    show: false,
    titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : undefined,
    backgroundColor: '#141414'
  });

  // Load the app
  if (isDev) {
    console.log('🌐 Loading development URL: http://localhost:5173');
    mainWindow.loadURL('http://localhost:5173').catch(error => {
      console.error('❌ Failed to load dev URL:', error);
    });
    mainWindow.webContents.openDevTools();
  } else {
    const htmlPath = join(__dirname, '../dist/index.html');
    console.log('📄 Loading production file:', htmlPath);
    mainWindow.loadFile(htmlPath).catch(error => {
      console.error('❌ Failed to load HTML file:', error);
    });
  }

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    mainWindow?.show();
    
    // Focus on the window
    if (isDev) {
      mainWindow?.focus();
    }
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });
}

// Create application menu
function createMenu() {
  const template: Electron.MenuItemConstructorOptions[] = [
    {
      label: 'File',
      submenu: [
        {
          label: 'Open Video...',
          accelerator: 'CmdOrCtrl+O',
          click: () => {
            mainWindow?.webContents.send('menu:open-video');
          }
        },
        { type: 'separator' },
        {
          label: 'Exit',
          accelerator: isWin ? 'Alt+F4' : 'CmdOrCtrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' }
      ]
    },
    {
      label: 'Help',
      submenu: [
        {
          label: 'About VideOCR',
          click: () => {
            dialog.showMessageBox(mainWindow!, {
              type: 'info',
              title: 'About VideOCR',
              message: 'VideOCR',
              detail: `Version: 1.3.1\nExtract subtitles from videos using OCR\n\nDeveloped by tangfeng2`
            });
          }
        },
        {
          label: 'GitHub Repository',
          click: () => {
            shell.openExternal('https://github.com/tangfeng2/VideOCR');
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// App event handlers
app.whenReady().then(() => {
  console.log('🚀 Electron app ready, creating window...');

  try {
    createWindow();
    createMenu();

    // Set dark theme
    nativeTheme.themeSource = 'dark';

    console.log('✅ Window created successfully');
  } catch (error) {
    console.error('❌ Error creating window:', error);
  }

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
}).catch(error => {
  console.error('❌ Error in app.whenReady():', error);
});

app.on('window-all-closed', () => {
  console.log('🪟 All windows closed');
  if (process.platform !== 'darwin') {
    console.log('🚪 Quitting app...');
    app.quit();
  }
});

// Add error handling
process.on('uncaughtException', (error) => {
  console.error('💥 Uncaught Exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('💥 Unhandled Rejection at:', promise, 'reason:', reason);
});

app.on('before-quit', () => {
  // Kill OCR process if running
  if (ocrProcess && ocrProcess.pid) {
    try {
      if (isWin) {
        spawn('taskkill', ['/pid', ocrProcess.pid.toString(), '/f', '/t'], {
          stdio: 'ignore'
        });
      } else {
        ocrProcess.kill('SIGTERM');
      }
    } catch (error) {
      console.error('Error killing OCR process on quit:', error);
    }
  }
});

// IPC Handlers
ipcMain.handle('video:select', async () => {
  const result = await dialog.showOpenDialog(mainWindow!, {
    title: 'Select Video File',
    filters: [
      {
        name: 'Video Files',
        extensions: ['mp4', 'avi', 'mkv', 'mov', 'webm', 'flv', 'wmv', 'ts', 'm2ts']
      },
      { name: 'All Files', extensions: ['*'] }
    ],
    properties: ['openFile']
  });

  return result.canceled ? null : result.filePaths[0];
});

ipcMain.handle('output:select', async (_, defaultPath: string) => {
  const result = await dialog.showSaveDialog(mainWindow!, {
    title: 'Save Subtitle File',
    defaultPath,
    filters: [
      { name: 'SubRip Subtitle', extensions: ['srt'] },
      { name: 'All Files', extensions: ['*'] }
    ]
  });

  return result.canceled ? null : result.filePath;
});

ipcMain.handle('folder:select', async () => {
  const result = await dialog.showOpenDialog(mainWindow!, {
    title: 'Select Output Directory',
    properties: ['openDirectory']
  });

  return result.canceled ? null : result.filePaths[0];
});

ipcMain.handle('videocr-cli:select', async () => {
  const result = await dialog.showOpenDialog(mainWindow!, {
    title: 'Select VideOCR CLI Executable',
    filters: [
      {
        name: 'Executable Files',
        extensions: isWin ? ['exe'] : ['bin', '*']
      },
      { name: 'All Files', extensions: ['*'] }
    ],
    properties: ['openFile']
  });

  return result.canceled ? null : result.filePaths[0];
});

ipcMain.handle('settings:save', async (_, settings) => {
  try {
    writeFileSync(CONFIG_FILE, JSON.stringify(settings, null, 2));
    return true;
  } catch (error) {
    console.error('Failed to save settings:', error);
    return false;
  }
});

ipcMain.handle('settings:load', async () => {
  try {
    if (existsSync(CONFIG_FILE)) {
      const data = readFileSync(CONFIG_FILE, 'utf-8');
      return JSON.parse(data);
    }
    return null;
  } catch (error) {
    console.error('Failed to load settings:', error);
    return null;
  }
});

ipcMain.handle('notification:send', async (_, title: string, message: string) => {
  if (mainWindow) {
    // Use Electron's built-in notification
    const notification = new Notification({
      title,
      body: message,
      icon: join(__dirname, '../assets/VideOCR.png')
    });

    notification.show();
  }
});

ipcMain.handle('taskbar:progress', async (_, progress: number) => {
  if (mainWindow && isWin) {
    mainWindow.setProgressBar(progress / 100);
  }
});

ipcMain.handle('taskbar:state', async (_, state: string) => {
  if (mainWindow && isWin) {
    if (state === 'normal') {
      mainWindow.setProgressBar(-1);
    }
  }
});

// OCR Process handling
ipcMain.handle('ocr:start', async (_, args) => {
  if (ocrProcess) {
    throw new Error('OCR process is already running');
  }

  return new Promise((resolve, reject) => {
    // Find videocr-cli executable
    let videOCRPath = '';

    // Check if custom path is provided
    if (args.customVideOCRPath && existsSync(args.customVideOCRPath)) {
      videOCRPath = args.customVideOCRPath;
      delete args.customVideOCRPath;
      console.log('Using custom VideOCR CLI:', videOCRPath);
    } else {
      // Use default bundled paths
      const possiblePaths = [
        join(process.resourcesPath, 'assets', 'videocr-cli-CPU', isWin ? 'videocr-cli.exe' : 'videocr-cli.bin'),
        join(process.resourcesPath, 'assets', 'videocr-cli-GPU', isWin ? 'videocr-cli.exe' : 'videocr-cli.bin'),
        join(__dirname, '..', 'assets', 'videocr-cli-CPU', isWin ? 'videocr-cli.exe' : 'videocr-cli.bin'),
        join(__dirname, '..', 'assets', 'videocr-cli-GPU', isWin ? 'videocr-cli.exe' : 'videocr-cli.bin')
      ];

      for (const path of possiblePaths) {
        if (existsSync(path)) {
          videOCRPath = path;
          break;
        }
      }

      console.log('Using bundled VideOCR CLI:', videOCRPath);
    }

    if (!videOCRPath) {
      reject(new Error('VideOCR CLI not found. Please select a custom path in settings.'));
      return;
    }

    // Build command arguments
    const command = [videOCRPath];

    console.log('🔍 Raw args received:', args);

    for (const [key, value] of Object.entries(args)) {
      if (value !== null && value !== undefined && value !== '') {
        console.log(`✅ Adding argument: --${key} ${value}`);
        command.push(`--${key}`);
        if (typeof value === 'boolean') {
          command.push(value.toString().toLowerCase());
        } else {
          command.push(String(value));
        }
      } else {
        console.log(`❌ Skipping argument: ${key} = ${value}`);
      }
    }

    console.log('🚀 OCR Command:', command.join(' '));

    // Start process
    try {
      // Set working directory to CLI directory to help with model path resolution
      const cliDir = videOCRPath.replace(/[^\/\\]+$/, '');
      const workingDir = cliDir || process.cwd();

      console.log('🔧 CLI path:', videOCRPath);
      console.log('🔧 Working directory:', workingDir);

      ocrProcess = spawn(command[0], command.slice(1), {
        stdio: ['pipe', 'pipe', 'pipe'],
        shell: false,
        windowsHide: true,
        cwd: workingDir
      });

      console.log('🔧 Process started successfully');
    } catch (error) {
      reject(new Error(`Failed to start OCR process: ${error}`));
      return;
    }

    let output = '';

    ocrProcess.stdout?.on('data', (data) => {
      const text = data.toString();
      output += text;

      // Send progress updates to renderer
      mainWindow?.webContents.send('ocr:progress', text);
    });

    ocrProcess.stderr?.on('data', (data) => {
      const text = data.toString();
      output += text;
      mainWindow?.webContents.send('ocr:progress', text);
    });

    ocrProcess.on('close', (code) => {
      ocrProcess = null;

      if (code === 0) {
        mainWindow?.webContents.send('ocr:complete', { success: true, output });

        // Send notification if enabled
        if (args.send_notification) {
          const notification = new Notification({
            title: 'VideOCR Complete',
            body: `Subtitle extraction finished: ${args.output}`,
            icon: join(__dirname, '../assets/VideOCR.png')
          });
          notification.show();
        }

        resolve({ success: true, code });
      } else {
        mainWindow?.webContents.send('ocr:complete', { success: false, output, code });
        resolve({ success: false, code });
      }
    });

    ocrProcess.on('error', (error) => {
      ocrProcess = null;
      mainWindow?.webContents.send('ocr:error', error.message);
      reject(error);
    });
  });
});

ipcMain.handle('ocr:cancel', async () => {
  if (ocrProcess && ocrProcess.pid) {
    try {
      if (isWin) {
        // Kill process tree on Windows
        spawn('taskkill', ['/pid', ocrProcess.pid.toString(), '/f', '/t'], {
          stdio: 'ignore'
        });
      } else {
        // Kill process group on Unix
        process.kill(-ocrProcess.pid, 'SIGTERM');
      }
    } catch (error) {
      console.error('Error killing OCR process:', error);
    }
    ocrProcess = null;
    return true;
  }
  return false;
});

// Add shell:openExternal handler for AboutTab
ipcMain.handle('shell:openExternal', async (_, url: string) => {
  shell.openExternal(url);
});

// Handle file path resolution for drag & drop
ipcMain.handle('file:getPath', async (_, file: any) => {
  // In Electron, dropped files should have a path property
  return file.path || file.name;
});

// Add video file serving
ipcMain.handle('video:getFileUrl', async (_, filePath: string) => {
  try {
    if (existsSync(filePath)) {
      // Return the file path that can be used in renderer
      return `file:///${filePath.replace(/\\/g, '/')}`;
    }
    return null;
  } catch (error) {
    console.error('Error getting video file URL:', error);
    return null;
  }
});

// Add video file reading for blob URL
ipcMain.handle('video:readFile', async (_, filePath: string) => {
  try {
    if (existsSync(filePath)) {
      const buffer = readFileSync(filePath);
      return buffer;
    }
    return null;
  } catch (error) {
    console.error('Error reading video file:', error);
    return null;
  }
});
