/* Global styles */
* {
  box-sizing: border-box;
}

html, body, #root {
  margin: 0;
  padding: 0;
  height: 100%;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #141414;
  color: #ffffff;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #1f1f1f;
}

::-webkit-scrollbar-thumb {
  background: #434343;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555555;
}

/* Video canvas styles */
.video-canvas {
  border: 1px solid #434343;
  background-color: #000000;
  cursor: crosshair;
}

.video-canvas.dragging {
  cursor: crosshair;
}

/* Crop box styles */
.crop-box {
  position: absolute;
  border: 2px solid #ff4d4f;
  background-color: rgba(255, 77, 79, 0.1);
  pointer-events: none;
}

/* Progress styles */
.progress-container {
  background-color: #1f1f1f;
  border: 1px solid #434343;
  border-radius: 6px;
  padding: 12px;
  margin: 8px 0;
}

.progress-text {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  white-space: pre-wrap;
  color: #a6a6a6;
}

/* Custom button styles */
.btn-primary {
  background-color: #1890ff;
  border-color: #1890ff;
}

.btn-primary:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

.btn-danger {
  background-color: #ff4d4f;
  border-color: #ff4d4f;
}

.btn-danger:hover {
  background-color: #ff7875;
  border-color: #ff7875;
}

/* Layout styles */
.app-layout {
  height: 100vh;
  background-color: #141414;
}

.content-area {
  padding: 16px;
  background-color: #141414;
  overflow-y: auto;
  height: calc(100vh - 64px);
}

/* Tab styles */
.ant-tabs-content-holder {
  overflow-y: auto;
  max-height: calc(100vh - 120px);
}

/* Form styles */
.form-row {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  gap: 12px;
}

.form-label {
  min-width: 200px;
  color: #ffffff;
  font-weight: 500;
}

.form-input {
  flex: 1;
}

/* Video controls */
.video-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background-color: #1f1f1f;
  border-radius: 6px;
  margin: 8px 0;
}

/* Drag and drop styles */
.drag-drop-area {
  position: relative;
  transition: all 0.3s ease;
}

.drag-drop-area.dragging {
  background-color: rgba(24, 144, 255, 0.1) !important;
  border: 2px dashed #1890ff !important;
}

.drag-drop-area.dragging::after {
  content: "📁 Drop video file here";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(24, 144, 255, 0.9);
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  pointer-events: none;
  z-index: 10;
}

.frame-info {
  color: #a6a6a6;
  font-size: 12px;
  white-space: nowrap;
}

/* Loading animations */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

.pulse-animation {
  animation: pulse 2s ease-in-out infinite;
}

/* Smooth transitions */
.ant-tabs-content-holder {
  transition: all 0.3s ease;
}

.ant-btn {
  transition: all 0.2s ease;
}

.ant-input {
  transition: all 0.2s ease;
}

.ant-select-selector {
  transition: all 0.2s ease;
}

/* Custom focus styles */
.ant-input:focus,
.ant-input-focused {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.ant-select-focused .ant-select-selector {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* Progress bar enhancements */
.ant-progress-line {
  margin-bottom: 8px;
}

.ant-progress-text {
  color: #ffffff !important;
}

/* Card enhancements */
.ant-card {
  border: 1px solid #434343;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.ant-card-head {
  border-bottom: 1px solid #434343;
}

.ant-card-head-title {
  color: #ffffff;
  font-weight: 600;
}

/* Button enhancements */
.ant-btn-primary {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
}

.ant-btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.4);
  transform: translateY(-1px);
}

.ant-btn-danger {
  background: linear-gradient(135deg, #ff4d4f 0%, #cf1322 100%);
  border: none;
  box-shadow: 0 2px 4px rgba(255, 77, 79, 0.3);
}

.ant-btn-danger:hover {
  background: linear-gradient(135deg, #ff7875 0%, #ff4d4f 100%);
  box-shadow: 0 4px 8px rgba(255, 77, 79, 0.4);
  transform: translateY(-1px);
}

/* Tooltip enhancements */
.ant-tooltip-inner {
  background-color: #1f1f1f;
  border: 1px solid #434343;
  color: #ffffff;
}

.ant-tooltip-arrow::before {
  background-color: #1f1f1f;
  border: 1px solid #434343;
}

/* Modal enhancements */
.ant-modal-content {
  background-color: #1f1f1f;
  border: 1px solid #434343;
}

.ant-modal-header {
  background-color: #1f1f1f;
  border-bottom: 1px solid #434343;
}

.ant-modal-title {
  color: #ffffff;
}

.ant-modal-body {
  color: #ffffff;
}

/* Notification enhancements */
.ant-notification {
  background-color: #1f1f1f;
  border: 1px solid #434343;
  color: #ffffff;
}

/* Responsive design */
@media (max-width: 1200px) {
  .content-area {
    padding: 12px;
  }

  .ant-card {
    margin-bottom: 12px;
  }
}

@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    align-items: stretch;
  }

  .form-label {
    min-width: auto;
    margin-bottom: 4px;
  }

  .video-controls {
    flex-wrap: wrap;
  }

  .content-area {
    padding: 8px;
  }

  .ant-tabs-tab {
    padding: 8px 12px;
    font-size: 12px;
  }

  .ant-card-head-title {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .video-canvas {
    max-width: 100%;
    height: auto;
  }

  .video-controls {
    flex-direction: column;
    gap: 8px;
  }

  .frame-info {
    text-align: center;
  }

  .ant-btn {
    width: 100%;
  }

  .ant-space {
    width: 100%;
  }

  .ant-space-item {
    width: 100%;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .ant-card {
    border: 2px solid #ffffff;
  }

  .ant-btn {
    border: 2px solid currentColor;
  }

  .video-canvas {
    border: 2px solid #ffffff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
