export class TimeUtils {
  /**
   * Parse time string in MM:SS or HH:MM:SS format to seconds
   */
  static parseTimeToSeconds(timeStr: string): number | null {
    if (!timeStr || timeStr.trim().length === 0) {
      return null;
    }

    const parts = timeStr.split(':').map(part => parseInt(part, 10));
    
    if (parts.some(part => isNaN(part) || part < 0)) {
      return null;
    }

    if (parts.length === 2) {
      // MM:SS format
      const [minutes, seconds] = parts;
      if (seconds >= 60) return null;
      return minutes * 60 + seconds;
    } else if (parts.length === 3) {
      // HH:MM:SS format
      const [hours, minutes, seconds] = parts;
      if (minutes >= 60 || seconds >= 60) return null;
      return hours * 3600 + minutes * 60 + seconds;
    }

    return null;
  }

  /**
   * Format seconds to HH:MM:SS or MM:SS string
   */
  static formatSecondsToTime(totalSeconds: number): string {
    const seconds = Math.floor(totalSeconds);
    const h = Math.floor(seconds / 3600);
    const m = Math.floor((seconds % 3600) / 60);
    const s = seconds % 60;

    if (h > 0) {
      return `${h.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}:${s.toString().padStart(2, '0')}`;
    } else {
      return `${m.toString().padStart(2, '0')}:${s.toString().padStart(2, '0')}`;
    }
  }

  /**
   * Validate time format (MM:SS or HH:MM:SS)
   */
  static isValidTimeFormat(timeStr: string): boolean {
    if (!timeStr || timeStr.trim().length === 0) {
      return true; // Empty string is valid (optional field)
    }

    const timeRegex = /^(\d{1,2}:\d{2}|\d{1,2}:\d{2}:\d{2})$/;
    if (!timeRegex.test(timeStr)) {
      return false;
    }

    // Additional validation - check if parsed time is valid
    return this.parseTimeToSeconds(timeStr) !== null;
  }

  /**
   * Convert frame number to time based on FPS
   */
  static frameToTime(frameNumber: number, fps: number): number {
    return frameNumber / fps;
  }

  /**
   * Convert time to frame number based on FPS
   */
  static timeToFrame(timeSeconds: number, fps: number): number {
    return Math.floor(timeSeconds * fps);
  }

  /**
   * Format duration in a human-readable way
   */
  static formatDuration(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  }

  /**
   * Get current timestamp string
   */
  static getCurrentTimestamp(): string {
    const now = new Date();
    return now.toISOString().replace('T', ' ').substring(0, 19);
  }

  /**
   * Calculate time remaining based on progress
   */
  static calculateTimeRemaining(
    startTime: number,
    currentProgress: number,
    totalProgress: number
  ): number | null {
    if (currentProgress <= 0 || totalProgress <= 0 || currentProgress >= totalProgress) {
      return null;
    }

    const elapsed = Date.now() - startTime;
    const progressRatio = currentProgress / totalProgress;
    const estimatedTotal = elapsed / progressRatio;
    const remaining = estimatedTotal - elapsed;

    return Math.max(0, remaining / 1000); // Convert to seconds
  }

  /**
   * Debounce function for time-based operations
   */
  static debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout;
    
    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), wait);
    };
  }

  /**
   * Throttle function for time-based operations
   */
  static throttle<T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): (...args: Parameters<T>) => void {
    let inThrottle: boolean;
    
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }
}
