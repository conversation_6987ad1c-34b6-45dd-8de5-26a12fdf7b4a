; NSIS installer script for VideOCR

; Custom installer pages
!include "MUI2.nsh"

; Installer settings
!define MUI_ICON "VideOCR.ico"
!define MUI_UNICON "VideOCR.ico"
!define MUI_HEADERIMAGE
!define MUI_HEADERIMAGE_BITMAP "VideOCR.png"
!define MUI_WELCOMEFINISHPAGE_BITMAP "VideOCR.png"

; Pages
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

!insertmacro MUI_UNPAGE_WELCOME
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES
!insertmacro MUI_UNPAGE_FINISH

; Languages
!insertmacro MUI_LANGUAGE "English"

; Custom functions
Function .onInit
  ; Check if application is already running
  System::Call 'kernel32::CreateMutex(i 0, i 0, t "VideOCR-Installer") i .r1 ?e'
  Pop $R0
  StrCmp $R0 0 +3
    MessageBox MB_OK|MB_ICONEXCLAMATION "The installer is already running."
    Abort
FunctionEnd

Function un.onInit
  MessageBox MB_ICONQUESTION|MB_YESNO|MB_DEFBUTTON2 "Are you sure you want to completely remove $(^Name) and all of its components?" IDYES +2
  Abort
FunctionEnd
