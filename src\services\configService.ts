import { Settings, CropBox } from '@/types';
import { DEFAULT_SETTINGS } from '@/constants';

export class ConfigService {
  /**
   * Load settings from electron main process
   */
  static async loadSettings(): Promise<Settings> {
    try {
      const savedSettings = await window.electronAPI.ipcRenderer.invoke('settings:load');

      if (savedSettings) {
        // Merge with defaults to ensure all properties exist
        const mergedSettings = { ...DEFAULT_SETTINGS, ...savedSettings };

        // Validate and sanitize loaded settings
        return this.validateAndSanitizeSettings(mergedSettings);
      }

      return { ...DEFAULT_SETTINGS };
    } catch (error) {
      console.error('Failed to load settings:', error);
      return { ...DEFAULT_SETTINGS };
    }
  }

  /**
   * Save settings to electron main process
   */
  static async saveSettings(settings: Settings): Promise<boolean> {
    try {
      // Validate settings before saving
      const validatedSettings = this.validateAndSanitizeSettings(settings);

      await window.electronAPI.ipcRenderer.invoke('settings:save', validatedSettings);

      return true;
    } catch (error) {
      console.error('Failed to save settings:', error);
      return false;
    }
  }

  /**
   * Validate and sanitize settings object
   */
  static validateAndSanitizeSettings(settings: Partial<Settings>): Settings {
    const sanitized: Settings = { ...DEFAULT_SETTINGS };

    // String settings
    if (typeof settings.language === 'string') {
      sanitized.language = settings.language;
    }
    
    if (typeof settings.subtitlePosition === 'string') {
      sanitized.subtitlePosition = settings.subtitlePosition;
    }
    
    if (typeof settings.timeStart === 'string') {
      sanitized.timeStart = settings.timeStart;
    }
    
    if (typeof settings.timeEnd === 'string') {
      sanitized.timeEnd = settings.timeEnd;
    }
    
    if (typeof settings.defaultOutputDir === 'string') {
      sanitized.defaultOutputDir = settings.defaultOutputDir;
    }

    if (typeof settings.customVideOCRPath === 'string') {
      sanitized.customVideOCRPath = settings.customVideOCRPath;
    }

    // Numeric settings with validation
    const numericSettings = [
      { key: 'confThreshold', min: 0, max: 100 },
      { key: 'simThreshold', min: 0, max: 100 },
      { key: 'ssimThreshold', min: 0, max: 100 },
      { key: 'maxMergeGap', min: 0, max: null },
      { key: 'minSubtitleDuration', min: 0, max: null },
      // { key: 'ocrImageMaxWidth', min: 0, max: null },
      { key: 'framesToSkip', min: 0, max: null },
      { key: 'keyboardSeekStep', min: 1, max: null }
    ] as const;

    for (const setting of numericSettings) {
      const value = settings[setting.key];
      if (typeof value === 'number' && !isNaN(value)) {
        let sanitizedValue = value;
        
        if (sanitizedValue < setting.min) {
          sanitizedValue = setting.min;
        }
        
        if (setting.max !== null && sanitizedValue > setting.max) {
          sanitizedValue = setting.max;
        }
        
        (sanitized as any)[setting.key] = sanitizedValue;
      }
    }

    // Optional numeric setting
    if (typeof settings.brightnessThreshold === 'number' && !isNaN(settings.brightnessThreshold)) {
      const value = Math.max(0, Math.min(255, settings.brightnessThreshold));
      sanitized.brightnessThreshold = value;
    }

    // Boolean settings
    const booleanSettings = [
      'useFullframe',
      'useGpu',
      'useAngleCls',
      'postProcessing',
      'useServerModel',
      'useDualZone',
      'saveInVideoDir',
      'sendNotification',
      'saveCropBox'
    ] as const;

    for (const setting of booleanSettings) {
      if (typeof settings[setting] === 'boolean') {
        sanitized[setting] = settings[setting];
      }
    }

    // Array settings
    if (Array.isArray(settings.savedCropBoxes)) {
      sanitized.savedCropBoxes = this.validateCropBoxes(settings.savedCropBoxes);
    }

    return sanitized;
  }

  /**
   * Validate crop boxes array
   */
  static validateCropBoxes(cropBoxes: any[]): CropBox[] {
    const validated: CropBox[] = [];

    for (const box of cropBoxes) {
      if (
        typeof box === 'object' &&
        typeof box.x === 'number' &&
        typeof box.y === 'number' &&
        typeof box.width === 'number' &&
        typeof box.height === 'number' &&
        box.x >= 0 &&
        box.y >= 0 &&
        box.width > 0 &&
        box.height > 0
      ) {
        validated.push({
          x: Math.floor(box.x),
          y: Math.floor(box.y),
          width: Math.ceil(box.width),
          height: Math.ceil(box.height)
        });
      }
    }

    return validated;
  }

  /**
   * Export settings to JSON string
   */
  static exportSettings(settings: Settings): string {
    try {
      return JSON.stringify(settings, null, 2);
    } catch (error) {
      console.error('Failed to export settings:', error);
      return '';
    }
  }

  /**
   * Import settings from JSON string
   */
  static importSettings(jsonString: string): Settings | null {
    try {
      const parsed = JSON.parse(jsonString);
      return this.validateAndSanitizeSettings(parsed);
    } catch (error) {
      console.error('Failed to import settings:', error);
      return null;
    }
  }

  /**
   * Reset settings to defaults
   */
  static getDefaultSettings(): Settings {
    return { ...DEFAULT_SETTINGS };
  }

  /**
   * Check if settings have been modified from defaults
   */
  static hasModifiedSettings(settings: Settings): boolean {
    const defaults = DEFAULT_SETTINGS;
    
    // Compare all settings except savedCropBoxes (which can change during usage)
    const keysToCompare = Object.keys(defaults).filter(key => key !== 'savedCropBoxes');
    
    for (const key of keysToCompare) {
      if ((settings as any)[key] !== (defaults as any)[key]) {
        return true;
      }
    }
    
    return false;
  }

  /**
   * Get settings summary for display
   */
  static getSettingsSummary(settings: Settings): Record<string, any> {
    return {
      language: settings.language,
      subtitlePosition: settings.subtitlePosition,
      useGpu: settings.useGpu,
      useFullframe: settings.useFullframe,
      useDualZone: settings.useDualZone,
      confThreshold: settings.confThreshold,
      simThreshold: settings.simThreshold,
      postProcessing: settings.postProcessing,
      useServerModel: settings.useServerModel,
      sendNotification: settings.sendNotification
    };
  }
}
