const fs = require('fs');

// Create necessary directories
const directories = [
  'assets',
  'assets/videocr-cli-CPU',
  'assets/videocr-cli-GPU',
  'dist',
  'dist-electron',
  'release'
];

console.log('Setting up VideOCR Electron project...');

directories.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`✓ Created directory: ${dir}`);
  } else {
    console.log(`✓ Directory exists: ${dir}`);
  }
});

// Create placeholder files for assets
const placeholderFiles = [
  'assets/VideOCR.ico',
  'assets/VideOCR.png',
  'assets/VideOCR.icns'
];

placeholderFiles.forEach(file => {
  if (!fs.existsSync(file)) {
    // Create empty placeholder file
    fs.writeFileSync(file, '');
    console.log(`✓ Created placeholder: ${file}`);
  }
});

// Check for VideOCR CLI
const cliPaths = [
  'assets/videocr-cli-CPU/videocr-cli.exe',
  'assets/videocr-cli-CPU/videocr-cli.bin',
  'assets/videocr-cli-GPU/videocr-cli.exe',
  'assets/videocr-cli-GPU/videocr-cli.bin'
];

let cliFound = false;
cliPaths.forEach(cliPath => {
  if (fs.existsSync(cliPath)) {
    console.log(`✓ Found VideOCR CLI: ${cliPath}`);
    cliFound = true;
  }
});

if (!cliFound) {
  console.log('⚠️  VideOCR CLI not found. Please download from:');
  console.log('   https://github.com/timminator/VideOCR/releases');
  console.log('   Extract to assets/videocr-cli-CPU/ or assets/videocr-cli-GPU/');
}

console.log('\nSetup complete! Run "npm run electron:dev" to start development.');
