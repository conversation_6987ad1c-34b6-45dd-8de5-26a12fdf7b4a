import React, { useEffect, useRef } from 'react';
import { Card, Typography, Progress } from 'antd';
import { useAppStore } from '@/stores/useAppStore';

const { Text, Title } = Typography;

const ProgressOutput: React.FC = () => {
  const { outputLog, progressInfo, processStatus } = useAppStore();
  const outputRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new output is added
  useEffect(() => {
    if (outputRef.current) {
      outputRef.current.scrollTop = outputRef.current.scrollHeight;
    }
  }, [outputLog]);

  // Process output log to handle terminal-like display
  const processedOutput = React.useMemo(() => {
    const fullText = outputLog.join('');
    const lines = fullText.split('\n').filter(line => line.trim() !== '');

    // Keep only the last 50 lines to prevent performance issues
    return lines.slice(-50);
  }, [outputLog]);

  // Parse progress from output log
  const parseProgress = () => {
    if (!outputLog.length) return null;

    const lastLines = outputLog.slice(-10).join('');
    
    // Look for progress patterns
    const step1Pattern = /Step 1: Processed image (\d+) of (\d+) \((\d+)%\)/;
    const step2Pattern = /Step 2: Performed OCR on image (\d+) of (\d+) \((\d+)%\)/;
    const vfrPattern = /Mapped frame (\d+) of (\d+) \((\d+)%\)/;
    const seekPattern = /Advanced to frame (\d+)\/(\d+) \((\d+)%\)/;

    let match = step2Pattern.exec(lastLines);
    if (match) {
      return {
        step: 'Step 2: OCR Processing',
        current: parseInt(match[1]),
        total: parseInt(match[2]),
        percentage: parseInt(match[3])
      };
    }

    match = step1Pattern.exec(lastLines);
    if (match) {
      return {
        step: 'Step 1: Image Processing',
        current: parseInt(match[1]),
        total: parseInt(match[2]),
        percentage: parseInt(match[3])
      };
    }

    match = vfrPattern.exec(lastLines);
    if (match) {
      return {
        step: 'Building Timestamp Map',
        current: parseInt(match[1]),
        total: parseInt(match[2]),
        percentage: parseInt(match[3])
      };
    }

    match = seekPattern.exec(lastLines);
    if (match) {
      return {
        step: 'Seeking to Start Time',
        current: parseInt(match[1]),
        total: parseInt(match[2]),
        percentage: parseInt(match[3])
      };
    }

    return null;
  };

  const currentProgress = parseProgress();

  return (
    <Card 
      title={<Title level={4}>Progress Info</Title>} 
      size="small"
      style={{ height: '300px', display: 'flex', flexDirection: 'column' }}
    >
      {/* Progress Bar */}
      {processStatus.isRunning && currentProgress && (
        <div style={{ marginBottom: '12px' }}>
          <Text strong>{currentProgress.step}</Text>
          <Progress
            percent={currentProgress.percentage}
            status={processStatus.isRunning ? 'active' : 'normal'}
            format={(percent: any) => `${currentProgress.current}/${currentProgress.total} (${percent}%)`}
            style={{ marginTop: '4px' }}
          />
        </div>
      )}

      {/* Terminal-like Output Log */}
      <div
        ref={outputRef}
        style={{
          flex: 1,
          backgroundColor: '#0c0c0c',
          border: '1px solid #333',
          borderRadius: '4px',
          padding: '8px',
          fontFamily: 'Consolas, Monaco, "Courier New", monospace',
          fontSize: '11px',
          lineHeight: '1.2',
          color: '#00ff41',
          overflowY: 'auto',
          maxHeight: '250px',
          minHeight: '150px',
          whiteSpace: 'nowrap',
          scrollBehavior: 'smooth',
        }}
      >
        {processedOutput.length > 0 ? (
          processedOutput.map((line, index) => (
            <div
              key={index}
              style={{
                marginBottom: '1px',
                overflow: 'hidden',
                textOverflow: 'ellipsis'
              }}
            >
              <span style={{ color: '#666', marginRight: '8px' }}>
                {String(index + 1).padStart(3, '0')}:
              </span>
              {line}
            </div>
          ))
        ) : (
          <div style={{ color: '#666', fontStyle: 'italic' }}>
            Process output will appear here...
          </div>
        )}
      </div>
    </Card>
  );
};

export default ProgressOutput;
