{"appId": "com.tangfeng2.videocr", "productName": "VideOCR", "copyright": "Copyright © 2024 tangfeng2", "directories": {"output": "release", "buildResources": "assets"}, "files": ["dist/**/*", "dist-electron/**/*", "node_modules/**/*", "package.json"], "extraResources": [{"from": "assets", "to": "assets", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}, {"target": "portable", "arch": ["x64"]}], "icon": "assets/VideOCR.ico", "requestedExecutionLevel": "asInvoker", "artifactName": "${productName}-${version}-${arch}.${ext}", "publisherName": "tangfeng2"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}, {"target": "zip", "arch": ["x64", "arm64"]}], "icon": "assets/VideOCR.icns", "category": "public.app-category.video", "artifactName": "${productName}-${version}-${arch}.${ext}", "hardenedRuntime": true, "gatekeeperAssess": false, "entitlements": "assets/entitlements.mac.plist", "entitlementsInherit": "assets/entitlements.mac.plist"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}, {"target": "rpm", "arch": ["x64"]}], "icon": "assets/VideOCR.png", "category": "AudioVideo", "artifactName": "${productName}-${version}-${arch}.${ext}", "desktop": {"Name": "VideOCR", "Comment": "Extract subtitles from videos using OCR", "Keywords": "video;subtitle;ocr;extract;srt;", "StartupWMClass": "VideOCR"}}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "VideOCR", "include": "assets/installer.nsh", "artifactName": "${productName}-Setup-${version}.${ext}", "deleteAppDataOnUninstall": false}, "dmg": {"title": "${productName} ${version}", "artifactName": "${productName}-${version}.${ext}", "background": "assets/dmg-background.png", "icon": "assets/VideOCR.icns", "iconSize": 100, "contents": [{"x": 380, "y": 280, "type": "link", "path": "/Applications"}, {"x": 110, "y": 280, "type": "file"}], "window": {"width": 540, "height": 380}}, "publish": {"provider": "github", "owner": "tangfeng2", "repo": "VideOCR"}, "buildVersion": "1.3.1"}