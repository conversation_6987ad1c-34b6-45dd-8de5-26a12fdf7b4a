// Language types
export interface Language {
  name: string;
  code: string;
}

// Subtitle position types
export interface SubtitlePosition {
  name: string;
  value: string;
}

// Video metadata
export interface VideoMetadata {
  path: string;
  width: number;
  height: number;
  totalFrames: number;
  fps: number;
  duration: number;
}

// Crop box coordinates
export interface CropBox {
  x: number;
  y: number;
  width: number;
  height: number;
}

// OCR Settings
export interface OCRSettings {
  language: string;
  subtitlePosition: string;
  timeStart: string;
  timeEnd: string;
  confThreshold: number;
  simThreshold: number;
  maxMergeGap: number;
  brightnessThreshold?: number;
  ssimThreshold: number;
  ocrImageMaxWidth: number;
  framesToSkip: number;
  minSubtitleDuration: number;
  useFullframe: boolean;
  useGpu: boolean;
  useAngleCls: boolean;
  postProcessing: boolean;
  useServerModel: boolean;
  useDualZone: boolean;
}

// App Settings
export interface AppSettings {
  keyboardSeekStep: number;
  defaultOutputDir: string;
  saveInVideoDir: boolean;
  sendNotification: boolean;
  saveCropBox: boolean;
  savedCropBoxes: CropBox[];
  customVideOCRPath: string; // Custom path to videocr-cli.exe
}

// Combined settings
export interface Settings extends OCRSettings, AppSettings {}

// Progress tracking
export interface ProgressInfo {
  step: string;
  current: number;
  total: number;
  percentage: number;
  message: string;
}

// Process status
export interface ProcessStatus {
  isRunning: boolean;
  canCancel: boolean;
  progress?: ProgressInfo;
}

// File paths
export interface FilePaths {
  videoPath: string;
  outputPath: string;
}

// IPC Events
export interface IPCEvents {
  'video:select': () => Promise<string | null>;
  'output:select': (defaultPath: string) => Promise<string | null>;
  'folder:select': () => Promise<string | null>;
  'ocr:start': (args: any) => Promise<void>;
  'ocr:cancel': () => Promise<void>;
  'settings:save': (settings: Settings) => Promise<void>;
  'settings:load': () => Promise<Settings>;
  'notification:send': (title: string, message: string) => Promise<void>;
  'taskbar:progress': (progress: number) => Promise<void>;
  'taskbar:state': (state: 'normal' | 'indeterminate' | 'error' | 'paused') => Promise<void>;
}

// Window API
export interface WindowAPI {
  ipcRenderer: {
    invoke: <K extends keyof IPCEvents>(channel: K, ...args: Parameters<IPCEvents[K]>) => ReturnType<IPCEvents[K]>;
    on: (channel: string, callback: (...args: any[]) => void) => void;
    removeAllListeners: (channel: string) => void;
  };
  platform: string;
  versions: {
    node: string;
    chrome: string;
    electron: string;
  };
}

declare global {
  interface Window {
    electronAPI: WindowAPI;
  }
}
