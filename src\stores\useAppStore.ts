import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { Settings, VideoMetadata, CropBox, ProgressInfo, ProcessStatus } from '@/types';
import { DEFAULT_SETTINGS } from '@/constants';
import { ConfigService } from '@/services/configService';

interface AppState {
  // Settings
  settings: Settings;

  // Video state
  videoMetadata: VideoMetadata;
  currentFrame: number;
  cropBoxes: CropBox[];

  // Process state
  processStatus: ProcessStatus;
  progressInfo: ProgressInfo | null;
  outputLog: string[];

  // UI state
  activeTab: string;

  // Internal state
  _saveTimeout: NodeJS.Timeout | null;

  // Actions
  updateSettings: (updates: Partial<Settings>) => void;
  loadSettings: () => Promise<void>;
  saveSettings: () => Promise<void>;
  
  setVideoMetadata: (metadata: VideoMetadata) => void;
  setCurrentFrame: (frame: number) => void;
  setCropBoxes: (boxes: CropBox[]) => void;
  addCropBox: (box: CropBox) => void;
  clearCropBoxes: () => void;
  
  setProcessStatus: (status: Partial<ProcessStatus>) => void;
  setProgressInfo: (info: ProgressInfo | null) => void;
  addOutputLog: (message: string) => void;
  clearOutputLog: () => void;
  
  setActiveTab: (tab: string) => void;
  cleanup: () => void;
}

export const useAppStore = create<AppState>()(
  immer((set, get) => ({
    // Initial state
    settings: { ...DEFAULT_SETTINGS },
    videoMetadata: {
      path: '',
      width: 0,
      height: 0,
      totalFrames: 0,
      fps: 0,
      duration: 0
    },
    currentFrame: 0,
    cropBoxes: [],
    processStatus: {
      isRunning: false,
      canCancel: false
    },
    progressInfo: null,
    outputLog: [],
    activeTab: 'video',
    _saveTimeout: null,
    
    // Settings actions
    updateSettings: (updates) => {
      set((state) => {
        Object.assign(state.settings, updates);

        // Clear existing timeout
        if (state._saveTimeout) {
          clearTimeout(state._saveTimeout);
        }

        // Set new timeout for auto-save (debounced)
        state._saveTimeout = setTimeout(() => {
          get().saveSettings();
          set((state) => {
            state._saveTimeout = null;
          });
        }, 500);
      });
    },
    
    loadSettings: async () => {
      try {
        const loadedSettings = await ConfigService.loadSettings();
        set((state) => {
          state.settings = loadedSettings;
        });
      } catch (error) {
        console.error('Failed to load settings:', error);
      }
    },

    saveSettings: async () => {
      try {
        const { settings } = get();
        await ConfigService.saveSettings(settings);
      } catch (error) {
        console.error('Failed to save settings:', error);
      }
    },
    
    // Video actions
    setVideoMetadata: (metadata) => {
      // console.log('🏪 Store: Setting video metadata:', metadata);
      set((state) => {
        state.videoMetadata = metadata;
        state.currentFrame = 0;
        state.cropBoxes = [];
      });
      console.log('🏪 Store: Video metadata set successfully');
    },
    
    setCurrentFrame: (frame) => {
      set((state) => {
        state.currentFrame = frame;
      });
    },
    
    setCropBoxes: (boxes) => {
      set((state) => {
        state.cropBoxes = boxes;
      });
    },
    
    addCropBox: (box) => {
      set((state) => {
        const maxBoxes = state.settings.useDualZone ? 2 : 1;
        if (state.cropBoxes.length >= maxBoxes) {
          state.cropBoxes = [box];
        } else {
          state.cropBoxes.push(box);
        }
      });
    },
    
    clearCropBoxes: () => {
      set((state) => {
        state.cropBoxes = [];
      });
    },
    
    // Process actions
    setProcessStatus: (status) => {
      set((state) => {
        Object.assign(state.processStatus, status);
      });
    },
    
    setProgressInfo: (info) => {
      set((state) => {
        state.progressInfo = info;
      });
    },
    
    addOutputLog: (message) => {
      set((state) => {
        // Limit log size to prevent memory issues
        const MAX_LOG_LINES = 50;

        state.outputLog.push(message);

        // Keep only the last MAX_LOG_LINES entries
        if (state.outputLog.length > MAX_LOG_LINES) {
          state.outputLog = state.outputLog.slice(-MAX_LOG_LINES);
        }
      });
    },
    
    clearOutputLog: () => {
      set((state) => {
        state.outputLog = [];
      });
    },
    
    // UI actions
    setActiveTab: (tab) => {
      set((state) => {
        state.activeTab = tab;
      });
    },

    // Cleanup
    cleanup: () => {
      set((state) => {
        if (state._saveTimeout) {
          clearTimeout(state._saveTimeout);
          state._saveTimeout = null;
        }
      });
    }
  }))
);
