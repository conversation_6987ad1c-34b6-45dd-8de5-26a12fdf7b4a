import React, { useRef, useEffect, useState, useCallback } from 'react';
import { Slider, Button, Space, Typography } from 'antd';
import { LeftOutlined, RightOutlined, ClearOutlined, PlayCircleOutlined } from '@ant-design/icons';
import { useAppStore } from '@/stores/useAppStore';
import { CropBox } from '@/types';
import { GRAPH_SIZE } from '@/constants';

const { Text } = Typography;

interface VideoPlayerProps {
  videoPath: string;
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({ videoPath }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [startPoint, setStartPoint] = useState<{ x: number; y: number } | null>(null);
  const [currentPoint, setCurrentPoint] = useState<{ x: number; y: number } | null>(null);

  // console.log('🎬 VideoPlayer rendered with path:', videoPath);

  const {
    videoMetadata,
    currentFrame,
    cropBoxes,
    settings,
    setVideoMetadata,
    setCurrentFrame,
    addCropBox,
    clearCropBoxes
  } = useAppStore();

  // Load video metadata
  useEffect(() => {
    if (!videoPath || !videoRef.current) return;

    const video = videoRef.current;

    const handleLoadedMetadata = () => {
      try {
        // console.log('📊 Video metadata event fired');
        // console.log('📐 Video dimensions:', video.videoWidth, 'x', video.videoHeight);
        // console.log('⏱️ Video duration:', video.duration);
        // console.log('🎮 Video ready state:', video.readyState);

        // Try to set metadata even with basic info for testing
        const metadata = {
          path: videoPath,
          width: video.videoWidth || 640,
          height: video.videoHeight || 360,
          totalFrames: Math.floor((video.duration || 1) * 30),
          fps: 30,
          duration: video.duration || 1
        };

        // console.log('✅ Setting video metadata (forced):', metadata);
        setVideoMetadata(metadata);
        setCurrentFrame(0);

        // Try to draw frame
        try {
          drawFrame();
          // console.log('✅ Frame drawn successfully');
        } catch (error) {
          console.error('❌ Error drawing frame:', error);
        }

        // console.log('✅ Video metadata process completed');
      } catch (error) {
        console.error('❌ Error loading video metadata:', error);
      }
    };

    const handleError = (error: Event) => {
      console.error('❌ Video loading error:', error);
      console.error('Video element error:', (error.target as HTMLVideoElement)?.error);
    };

    const handleCanPlay = () => {
      console.log('✅ Video can play');
    };

    const loadVideo = async () => {
      try {
        // Add all video event listeners for debugging
        const events = [
          'loadstart', 'durationchange', 'loadedmetadata', 'loadeddata',
          'progress', 'canplay', 'canplaythrough', 'error', 'abort',
          'emptied', 'stalled', 'suspend', 'waiting'
        ];

        // events.forEach(eventName => {
        //   video.addEventListener(eventName, (e) => {
        //     console.log(`🎥 Video event: ${eventName}`, e);
        //   });
        // });

        video.addEventListener('loadedmetadata', handleLoadedMetadata);
        video.addEventListener('error', handleError);
        // video.addEventListener('canplay', handleCanPlay);

        // Try different URL formats
        const normalizedPath = videoPath.replace(/\\/g, '/');
        const fileUrl = normalizedPath.startsWith('file://')
          ? normalizedPath
          : `file:///${normalizedPath}`;

        // console.log('🎥 Loading video from:', videoPath);
        // console.log('🔗 Using URL:', fileUrl);

        // Set video properties
        video.preload = 'metadata';
        // Remove crossOrigin for local files
        // video.crossOrigin = 'anonymous';
        video.src = fileUrl;
        video.load(); // Force reload

        // console.log('🔄 Video load() called');

        // Fallback: Force metadata after 2 seconds if not loaded
        setTimeout(() => {
          if (!videoMetadata) {
            // console.log('⏰ Timeout: Forcing metadata load');
            handleLoadedMetadata();
          }
        }, 2000);
      } catch (error) {
        console.error('❌ Error setting up video:', error);
      }
    };

    loadVideo();

    return () => {
      video.removeEventListener('loadedmetadata', handleLoadedMetadata);
      video.removeEventListener('error', handleError);
      // video.removeEventListener('canplay', handleCanPlay);
    };
  }, [videoPath, setVideoMetadata, setCurrentFrame]);

  // Draw current frame on canvas
  const drawFrame = useCallback(() => {
    const canvas = canvasRef.current;
    const video = videoRef.current;
    
    if (!canvas || !video || !videoMetadata) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.fillStyle = '#000000';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Calculate scaling to fit video in canvas while maintaining aspect ratio
    const videoAspect = videoMetadata.width / videoMetadata.height;
    const canvasAspect = canvas.width / canvas.height;

    let drawWidth, drawHeight, offsetX, offsetY;

    if (videoAspect > canvasAspect) {
      // Video is wider than canvas
      drawWidth = canvas.width;
      drawHeight = canvas.width / videoAspect;
      offsetX = 0;
      offsetY = (canvas.height - drawHeight) / 2;
    } else {
      // Video is taller than canvas
      drawWidth = canvas.height * videoAspect;
      drawHeight = canvas.height;
      offsetX = (canvas.width - drawWidth) / 2;
      offsetY = 0;
    }

    // Draw video frame
    ctx.drawImage(video, offsetX, offsetY, drawWidth, drawHeight);

    // Draw existing crop boxes
    ctx.strokeStyle = '#ff4d4f';
    ctx.lineWidth = 2;
    
    cropBoxes.forEach((box) => {
      const scaleX = drawWidth / videoMetadata.width;
      const scaleY = drawHeight / videoMetadata.height;
      
      const x = offsetX + box.x * scaleX;
      const y = offsetY + box.y * scaleY;
      const width = box.width * scaleX;
      const height = box.height * scaleY;
      
      ctx.strokeRect(x, y, width, height);
      
      // Semi-transparent fill
      ctx.fillStyle = 'rgba(255, 77, 79, 0.1)';
      ctx.fillRect(x, y, width, height);
    });

    // Draw current selection if drawing
    if (isDrawing && startPoint && currentPoint) {
      ctx.strokeStyle = '#ff4d4f';
      ctx.lineWidth = 2;
      
      const x = Math.min(startPoint.x, currentPoint.x);
      const y = Math.min(startPoint.y, currentPoint.y);
      const width = Math.abs(currentPoint.x - startPoint.x);
      const height = Math.abs(currentPoint.y - startPoint.y);
      
      ctx.strokeRect(x, y, width, height);
      ctx.fillStyle = 'rgba(255, 77, 79, 0.1)';
      ctx.fillRect(x, y, width, height);
    }
  }, [videoMetadata, cropBoxes, isDrawing, startPoint, currentPoint]);

  // Update frame when currentFrame changes
  useEffect(() => {
    if (!videoRef.current || !videoMetadata) return;

    const video = videoRef.current;
    const targetTime = currentFrame / videoMetadata.fps;
    
    if (Math.abs(video.currentTime - targetTime) > 0.1) {
      video.currentTime = targetTime;
    }
  }, [currentFrame, videoMetadata]);

  // Redraw when video time updates
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleTimeUpdate = () => {
      drawFrame();
    };

    video.addEventListener('timeupdate', handleTimeUpdate);
    video.addEventListener('seeked', handleTimeUpdate);

    return () => {
      video.removeEventListener('timeupdate', handleTimeUpdate);
      video.removeEventListener('seeked', handleTimeUpdate);
    };
  }, [drawFrame]);

  // Canvas mouse events for crop box selection
  const handleMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!videoMetadata || settings.useFullframe) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    // Check if we're at the max number of crop boxes
    const maxBoxes = settings.useDualZone ? 2 : 1;
    if (cropBoxes.length >= maxBoxes) {
      clearCropBoxes();
    }

    setStartPoint({ x, y });
    setCurrentPoint({ x, y });
    setIsDrawing(true);
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing || !startPoint) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    setCurrentPoint({ x, y });
    drawFrame();
  };

  const handleMouseUp = () => {
    if (!isDrawing || !startPoint || !currentPoint || !videoMetadata) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    // Calculate crop box in video coordinates
    const videoAspect = videoMetadata.width / videoMetadata.height;
    const canvasAspect = canvas.width / canvas.height;

    let drawWidth, drawHeight, offsetX, offsetY;

    if (videoAspect > canvasAspect) {
      drawWidth = canvas.width;
      drawHeight = canvas.width / videoAspect;
      offsetX = 0;
      offsetY = (canvas.height - drawHeight) / 2;
    } else {
      drawWidth = canvas.height * videoAspect;
      drawHeight = canvas.height;
      offsetX = (canvas.width - drawWidth) / 2;
      offsetY = 0;
    }

    const scaleX = videoMetadata.width / drawWidth;
    const scaleY = videoMetadata.height / drawHeight;

    const x1 = Math.max(0, (Math.min(startPoint.x, currentPoint.x) - offsetX) * scaleX);
    const y1 = Math.max(0, (Math.min(startPoint.y, currentPoint.y) - offsetY) * scaleY);
    const x2 = Math.min(videoMetadata.width, (Math.max(startPoint.x, currentPoint.x) - offsetX) * scaleX);
    const y2 = Math.min(videoMetadata.height, (Math.max(startPoint.y, currentPoint.y) - offsetY) * scaleY);

    const width = x2 - x1;
    const height = y2 - y1;

    // Only add if the box is large enough
    if (width > 10 && height > 10) {
      const cropBox: CropBox = {
        x: Math.floor(x1),
        y: Math.floor(y1),
        width: Math.ceil(width),
        height: Math.ceil(height)
      };

      addCropBox(cropBox);
    }

    setIsDrawing(false);
    setStartPoint(null);
    setCurrentPoint(null);
    drawFrame();
  };

  // Keyboard navigation
  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (!videoMetadata) return;

    const step = settings.keyboardSeekStep;
    
    if (e.key === 'ArrowLeft') {
      e.preventDefault();
      setCurrentFrame(Math.max(0, currentFrame - step));
    } else if (e.key === 'ArrowRight') {
      e.preventDefault();
      setCurrentFrame(Math.min(videoMetadata?.totalFrames - 1, currentFrame + step));
    }
  }, [currentFrame, videoMetadata, settings.keyboardSeekStep, setCurrentFrame]);

  useEffect(() => {
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown]);

  // Format time display
  const formatTime = (seconds: number) => {
    const h = Math.floor(seconds / 3600);
    const m = Math.floor((seconds % 3600) / 60);
    const s = Math.floor(seconds % 60);
    
    const pad = (n: number) => n.toString().padStart(2, '0');

    if (h > 0) {
      return `${pad(h)}:${pad(m)}:${pad(s)}`;
    }
    return `${pad(m)}:${pad(s)}`;

  };

  // if (!videoMetadata) {
  //   return (
  //     <div style={{
  //       width: GRAPH_SIZE.width,
  //       height: GRAPH_SIZE.height,
  //       backgroundColor: '#000',
  //       display: 'flex',
  //       flexDirection: 'column',
  //       alignItems: 'center',
  //       justifyContent: 'center',
  //       border: '1px solid #434343',
  //       padding: '20px'
  //     }}>
  //       <Text type="secondary">No video loaded</Text>
  //       {videoPath && (
  //         <div style={{ marginTop: '10px', textAlign: 'center' }}>
  //           <Text type="secondary" style={{ fontSize: '12px' }}>
  //             Loading: {videoPath.split(/[/\\]/).pop()}
  //           </Text>
  //           <br />
  //           <Text type="secondary" style={{ fontSize: '10px' }}>
  //             Check console for errors
  //           </Text>
  //         </div>
  //       )}
  //     </div>
  //   );
  // }

  const currentTime = currentFrame / videoMetadata?.fps;
  const totalTime = videoMetadata?.duration;

  return (
    <div>
      <canvas
        ref={canvasRef}
        width={GRAPH_SIZE.width}
        height={GRAPH_SIZE.height}
        className="video-canvas"
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        style={{ cursor: settings.useFullframe ? 'default' : 'crosshair' }}
      />
      
      <video
        ref={videoRef}
        style={{ display: 'none' }}
        preload="metadata"
      />

      <div className="video-controls">
        {/* play btn */}
        {/* <Button
          icon={<PlayCircleOutlined />}
          size="small"
          onClick={() => {
            if (videoRef.current) {
              if(videoRef.current.paused) {
                videoRef.current.play();
              } else {
                videoRef.current.pause();
              }
            }
          }}
        /> */}
        <Button
          icon={<LeftOutlined />}
          size="small"
          onClick={() => setCurrentFrame(Math.max(0, currentFrame - settings.keyboardSeekStep))}
          disabled={currentFrame === 0}
        />
        
        <Slider
          min={0}
          max={videoMetadata?.totalFrames - 1}
          value={currentFrame}
          onChange={setCurrentFrame}
          style={{ flex: 1, margin: '0 12px' }}
          tooltip={{ formatter: (value: number | undefined) => `Frame ${(value || 0) + 1}` }}
        />
        
        <Button
          icon={<RightOutlined />}
          size="small"
          onClick={() => setCurrentFrame(Math.min(videoMetadata?.totalFrames - 1, currentFrame + settings.keyboardSeekStep))}
          disabled={currentFrame === videoMetadata?.totalFrames - 1}
        />
        
        <Button
          icon={<ClearOutlined />}
          size="small"
          onClick={clearCropBoxes}
          disabled={cropBoxes.length === 0}
        >
          Clear Crop
        </Button>
      </div>

      <div className="frame-info">
        <Space split={<span>|</span>}>
          <Text type="secondary">Frame: {currentFrame + 1} / {videoMetadata?.totalFrames}</Text>
          <Text type="secondary">Time: {formatTime(currentTime)} / {formatTime(totalTime)}</Text>
        </Space>
      </div>

      {cropBoxes.length > 0 && (
        <div style={{ marginTop: 8 }}>
          <Text type="secondary">
            Crop Box: {cropBoxes.map((box, index) => 
              `${settings.useDualZone ? `Zone ${index + 1}: ` : ''}(${box.x}, ${box.y}, ${box.width}, ${box.height})`
            ).join(' | ')}
          </Text>
        </div>
      )}
    </div>
  );
};

export default VideoPlayer;
