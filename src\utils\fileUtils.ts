import { VIDEO_EXTENSIONS } from '@/constants';

export class FileUtils {
  /**
   * Check if a file path has a valid video extension
   */
  static isVideoFile(filePath: string): boolean {
    const extension = this.getFileExtension(filePath);
    return VIDEO_EXTENSIONS.includes(extension);
  }

  /**
   * Get file extension from path (including the dot)
   */
  static getFileExtension(filePath: string): string {
    const lastDot = filePath.lastIndexOf('.');
    return lastDot === -1 ? '' : filePath.substring(lastDot).toLowerCase();
  }

  /**
   * Get filename without extension
   */
  static getFileNameWithoutExtension(filePath: string): string {
    const fileName = this.getFileName(filePath);
    const lastDot = fileName.lastIndexOf('.');
    return lastDot === -1 ? fileName : fileName.substring(0, lastDot);
  }

  /**
   * Get filename from path
   */
  static getFileName(filePath: string): string {
    const normalizedPath = filePath.replace(/\\/g, '/');
    const lastSlash = normalizedPath.lastIndexOf('/');
    return lastSlash === -1 ? normalizedPath : normalizedPath.substring(lastSlash + 1);
  }

  /**
   * Get directory from path
   */
  static getDirectory(filePath: string): string {
    const normalizedPath = filePath.replace(/\\/g, '/');
    const lastSlash = normalizedPath.lastIndexOf('/');
    return lastSlash === -1 ? '' : normalizedPath.substring(0, lastSlash);
  }

  /**
   * Join path components
   */
  static joinPath(...components: string[]): string {
    return components
      .map(component => component.replace(/[/\\]+$/, ''))
      .filter(component => component.length > 0)
      .join('/');
  }

  /**
   * Generate unique output path by adding counter if file exists
   */
  static generateUniqueOutputPath(basePath: string): string {
    // This would need to be implemented with file system access
    // For now, return the base path
    return basePath;
  }

  /**
   * Validate file path format
   */
  static isValidPath(filePath: string): boolean {
    if (!filePath || filePath.trim().length === 0) {
      return false;
    }

    // Check for invalid characters (basic validation)
    const invalidChars = /[<>:"|?*]/;
    return !invalidChars.test(filePath);
  }

  /**
   * Format file size in human readable format
   */
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Get default documents directory based on platform
   */
  static getDefaultDocumentsDir(): string {
    const platform = window.electronAPI.platform;
    
    switch (platform) {
      case 'win32':
        return 'C:\\Users\\<USER>\\Documents';
      case 'darwin':
        return '~/Documents';
      case 'linux':
        return '~/Documents';
      default:
        return '~/Documents';
    }
  }

  /**
   * Sanitize filename by removing invalid characters
   */
  static sanitizeFileName(fileName: string): string {
    // Remove or replace invalid characters
    return fileName
      .replace(/[<>:"/\\|?*]/g, '_')
      .replace(/\s+/g, ' ')
      .trim();
  }
}
