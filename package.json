{"name": "videocr-electron", "version": "1.3.1", "description": "VideOCR - Extract subtitles from videos using OCR", "main": "dist-electron/main.js", "author": "tangfeng2", "license": "MIT", "private": true, "scripts": {"dev": "vite", "build": "vite build && electron-builder", "build:dir": "vite build && electron-builder --dir", "build:win": "vite build && electron-builder --win", "build:mac": "vite build && electron-builder --mac", "build:linux": "vite build && electron-builder --linux", "preview": "vite preview", "electron": "wait-on tcp:5173 && cross-env NODE_ENV=development electron .", "electron:dev": "concurrently -k \"npm run dev\" \"wait-on tcp:5173 && npm run electron\"", "electron:pack": "electron-builder", "electron:dist": "npm run build && electron-builder", "postinstall": "electron-builder install-app-deps"}, "dependencies": {"@ant-design/icons": "^5.2.6", "antd": "^5.12.8", "classnames": "^2.3.2", "dayjs": "^1.11.10", "immer": "^10.0.3", "lodash": "^4.17.21", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router": "^7.7.1", "zustand": "^4.4.7"}, "devDependencies": {"@types/lodash": "^4.14.202", "@types/node": "^20.10.4", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react-swc": "^3.11.0", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "electron": "^31", "electron-builder": "^24.9.1", "typescript": "^5.3.3", "vite": "^5.0.8", "vite-plugin-electron": "^0.28.1", "vite-plugin-electron-renderer": "^0.14.5", "wait-on": "^7.2.0"}, "build": {"appId": "com.tangfeng2.videocr", "productName": "VideOCR", "directories": {"output": "release"}, "files": ["dist/**/*", "dist-electron/**/*", "node_modules/**/*", "package.json"], "extraResources": [{"from": "assets", "to": "assets", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "assets/VideOCR.ico", "requestedExecutionLevel": "asInvoker"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "assets/VideOCR.icns", "category": "public.app-category.video"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}], "icon": "assets/VideOCR.png", "category": "AudioVideo"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}