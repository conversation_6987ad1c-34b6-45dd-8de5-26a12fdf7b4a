# VideOCR Electron

A modern Electron-based application for extracting subtitles from videos using OCR technology. Built with React, TypeScript, Ant Design, and Vite.

## Features

- 🎥 **Video Processing**: Support for multiple video formats (MP4, AVI, MKV, MOV, WebM, FLV, WMV, TS, M2TS)
- 🔤 **Multi-language OCR**: Support for 60+ languages
- 🎯 **Interactive Crop Selection**: Click and drag to select subtitle regions
- 🔄 **Dual-zone OCR**: Process two separate subtitle regions simultaneously
- ⚡ **GPU Acceleration**: Optional GPU support for faster processing
- 🎛️ **Advanced Settings**: Comprehensive OCR configuration options
- 📊 **Real-time Progress**: Live progress tracking with taskbar integration
- 🔔 **Notifications**: Cross-platform notification support
- 💾 **Settings Persistence**: Automatic saving and loading of preferences
- 🎨 **Modern UI**: Dark theme with responsive design

## Prerequisites

Before running the application, you need to have the VideOCR CLI tool:

1. Download the appropriate VideOCR CLI version from the [official repository](https://github.com/timminator/VideOCR/releases)
2. Extract the CLI tool to the `assets` folder in your project directory:
   ```
   assets/
   ├── videocr-cli-CPU-v1.3.1/
   │   └── videocr-cli.exe (Windows) or videocr-cli.bin (Linux/Mac)
   └── videocr-cli-GPU-v1.3.1/
       └── videocr-cli.exe (Windows) or videocr-cli.bin (Linux/Mac)
   ```

## Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd VideOCR-Electron
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Copy assets**:
   - Copy `VideOCR.ico` and `VideOCR.png` to the `assets` folder
   - Ensure VideOCR CLI tools are in the correct directories

## Development

### Running in Development Mode

```bash
# Start the development server
npm run electron:dev
```

This will:
- Start the Vite development server on port 5173
- Launch Electron with hot reload enabled
- Open developer tools automatically

### Building for Production

```bash
# Build the application
npm run build

# Build for specific platforms
npm run build:win    # Windows
npm run build:mac    # macOS
npm run build:linux  # Linux
```

### Project Structure

```
src/
├── components/          # React components
│   ├── VideoTab.tsx    # Main video processing interface
│   ├── VideoPlayer.tsx # Video player with crop selection
│   ├── SettingsTab.tsx # Advanced settings panel
│   ├── AboutTab.tsx    # About page
│   └── ProgressOutput.tsx # Progress display
├── services/           # Business logic services
│   ├── ocrService.ts   # OCR processing service
│   └── configService.ts # Configuration management
├── stores/             # Zustand state management
│   └── useAppStore.ts  # Main application store
├── types/              # TypeScript type definitions
│   └── index.ts
├── utils/              # Utility functions
│   ├── fileUtils.ts
│   └── timeUtils.ts
├── constants/          # Application constants
│   └── index.ts
└── styles/             # CSS styles
    └── global.css

electron/
├── main.ts             # Electron main process
└── preload.ts          # Preload script for IPC
```

## Usage

### Basic Workflow

1. **Select Video**: Click "Browse" to select a video file
2. **Choose Language**: Select the appropriate OCR language
3. **Set Crop Area**: Click and drag on the video preview to select subtitle regions
4. **Configure Settings**: Adjust OCR parameters in the Advanced Settings tab
5. **Run OCR**: Click "Run OCR" to start subtitle extraction
6. **Monitor Progress**: Watch real-time progress in the output panel

### Advanced Features

#### Crop Box Selection
- **Single Mode**: Select one subtitle region
- **Dual Zone Mode**: Enable in settings to select two separate regions
- **Full Frame Mode**: Process the entire video frame
- **Keyboard Navigation**: Use arrow keys to navigate frames

#### OCR Settings
- **Confidence Threshold**: Minimum confidence for text detection (0-100)
- **Similarity Threshold**: Threshold for merging similar text (0-100)
- **Time Range**: Process only specific parts of the video
- **GPU Acceleration**: Enable for faster processing (if supported)
- **Post Processing**: Automatic space insertion and text correction

#### Output Options
- **Save Location**: Choose between video directory or custom folder
- **File Naming**: Automatic unique naming to prevent overwrites
- **Format**: Standard SRT subtitle format

## Configuration

Settings are automatically saved and include:

- OCR language and position preferences
- Processing thresholds and parameters
- UI preferences and crop box positions
- Output directory and notification settings

Configuration files are stored in:
- **Windows**: `%LOCALAPPDATA%\VideOCR\config.json`
- **macOS/Linux**: `~/.config/VideOCR/config.json`

## Troubleshooting

### Common Issues

1. **VideOCR CLI not found**:
   - Ensure the CLI tool is in the correct `assets` folder
   - Check file permissions (executable on Linux/Mac)

2. **GPU acceleration not working**:
   - Verify GPU drivers are installed
   - Try CPU version if GPU version fails

3. **Poor OCR accuracy**:
   - Adjust confidence and similarity thresholds
   - Select more precise crop areas
   - Try different OCR languages
   - Enable post-processing

4. **Performance issues**:
   - Increase frames to skip for faster processing
   - Reduce OCR image max width
   - Enable GPU acceleration

### Debug Mode

Run with debug logging:
```bash
npm run electron:dev
```

Check the developer console for detailed error messages.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Original VideOCR CLI by [timminator](https://github.com/timminator/VideOCR)
- Built with [Electron](https://electronjs.org/), [React](https://reactjs.org/), and [Ant Design](https://ant.design/)
- OCR powered by PaddleOCR

## Support

For issues and feature requests, please visit the [GitHub Issues](https://github.com/tangfeng2/VideOCR/issues) page.
