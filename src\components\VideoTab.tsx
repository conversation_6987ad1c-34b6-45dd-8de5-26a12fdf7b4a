import React, { useState } from 'react';
import {
  Button,
  Input,
  Space,
  Typography,
  Row,
  Col,
  Card
} from 'antd';
import { 
  FolderOpenOutlined, 
  SaveOutlined, 
  PlayCircleOutlined, 
  StopOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons';
import { useAppStore } from '@/stores/useAppStore';
import { LANGUAGES, SUBTITLE_POSITIONS } from '@/constants';
import { OCRService } from '@/services/ocrService';
import { FileUtils } from '@/utils/fileUtils';
import VideoPlayer from './VideoPlayer';
import ProgressOutput from './ProgressOutput';
import useMessage from '@/hooks/useMessage';
import useModal from '@/hooks/useModal';

const { Text, Title } = Typography;

const VideoTab: React.FC = () => {
  const message = useMessage();
  const modal = useModal();
  const [videoPath, setVideoPath] = useState('');
  const [outputPath, setOutputPath] = useState('');
  const [isDragging, setIsDragging] = useState(false);

  // Debug: Check if constants are loaded
  // console.log('🔍 LANGUAGES length:', LANGUAGES.length);
  // console.log('🔍 SUBTITLE_POSITIONS length:', SUBTITLE_POSITIONS.length);
  // console.log('🔍 First language:', LANGUAGES[0]);
  // console.log('🔍 First position:', SUBTITLE_POSITIONS[0]);

  const {
    settings,
    processStatus,
    cropBoxes,
    videoMetadata,
    updateSettings,
    setProcessStatus,
    clearOutputLog
  } = useAppStore();

  // Handle video file selection
  const handleSelectVideo = async () => {
    try {
      const selectedPath = await window.electronAPI.ipcRenderer.invoke('video:select');
      console.log('📁 Selected video path:', selectedPath);

      if (selectedPath) {
        setVideoPath(selectedPath);
        console.log('✅ Video path set to:', selectedPath);

        // Generate default output path
        const defaultOutput = OCRService.generateOutputPath(
          selectedPath,
          settings.saveInVideoDir,
          settings.defaultOutputDir || FileUtils.getDefaultDocumentsDir()
        );
        setOutputPath(defaultOutput);
        console.log('📄 Output path set to:', defaultOutput);
      }
    } catch (error) {
      message.error('Failed to select video file');
      console.error('❌ Video selection error:', error);
    }
  };

  // Handle output file selection
  const handleSelectOutput = async () => {
    try {
      const selectedPath = await window.electronAPI.ipcRenderer.invoke('output:select', outputPath);
      if (selectedPath) {
        setOutputPath(selectedPath);
      }
    } catch (error) {
      message.error('Failed to select output file');
      console.error('Output selection error:', error);
    }
  };

  // Handle OCR process start
  const handleStartOCR = async () => {
    if (!videoPath) {
      message.error('Please select a video file first');
      return;
    }

    if (!outputPath) {
      message.error('Please specify an output file');
      return;
    }

    // Validate settings
    const errors = validateSettings();
    if (errors.length > 0) {
      message.error(`Validation errors: ${errors.join(', ')}`);
      return;
    }

    try {
      setProcessStatus({ isRunning: true, canCancel: true });
      clearOutputLog();

      // Build OCR arguments
      const args = buildOCRArgs();

      await OCRService.startOCR(args);
    } catch (error) {
      message.error('Failed to start OCR process');
      console.error('OCR start error:', error);
      setProcessStatus({ isRunning: false, canCancel: false });
    }
  };

  // Handle OCR process cancellation
  const handleCancelOCR = async () => {
    try {
      const cancelled = await OCRService.cancelOCR();
      if (cancelled) {
        setProcessStatus({ isRunning: false, canCancel: false });
        message.info('OCR process cancelled');
      }
    } catch (error) {
      message.error('Failed to cancel OCR process');
      console.error('OCR cancel error:', error);
    }
  };

  // Handle drag & drop for video files
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    const files = Array.from(e.dataTransfer.files);
    const videoFile = files.find(file => {
      const extension = file.name.toLowerCase().split('.').pop();
      return ['mp4', 'avi', 'mkv', 'mov', 'webm', 'flv', 'wmv', 'ts', 'm2ts'].includes(extension || '');
    });

    if (videoFile) {
      try {
        // Get the actual file path from Electron
        const filePath = (videoFile as any).path || videoFile.name;
        console.log('📁 Dropped video file:', filePath);

        if (filePath && filePath !== videoFile.name) {
          setVideoPath(filePath);

          // Generate default output path
          const defaultOutput = OCRService.generateOutputPath(
            filePath,
            settings.saveInVideoDir,
            settings.defaultOutputDir || FileUtils.getDefaultDocumentsDir()
          );
          setOutputPath(defaultOutput);
          console.log('📄 Output path set to:', defaultOutput);
          message.success(`Video file loaded: ${videoFile.name}`);
        } else {
          message.error('Could not get file path. Please use Browse button instead.');
        }
      } catch (error) {
        console.error('Error handling dropped file:', error);
        message.error('Error processing dropped file');
      }
    } else {
      message.error('Please drop a valid video file (MP4, AVI, MKV, MOV, WebM, FLV, WMV, TS, M2TS)');
    }
  };

  // Validate settings
  const validateSettings = (): string[] => {
    const errors = OCRService.validateSettings(settings);

    // Additional validation for dual zone mode
    if (settings.useDualZone && cropBoxes.length !== 2) {
      errors.push('Dual Zone OCR requires exactly 2 crop boxes');
    }

    // Time range validation if video metadata is available
    if (videoMetadata) {
      const timeErrors = OCRService.validateTimeRange(
        settings.timeStart,
        settings.timeEnd,
        videoMetadata.duration
      );
      errors.push(...timeErrors);
    }

    return errors;
  };

  // Build OCR arguments
  const buildOCRArgs = () => {
    return OCRService.buildOCRArgs(videoPath, outputPath, settings, cropBoxes);
  };

  // Show help modal
  const showHelp = () => {
    modal.info({
      title: 'How to Use VideOCR',
      width: 600,
      content: (
        <div>
          <p><strong>Crop Box Selection:</strong></p>
          <ul>
            <li>Click and drag on the video preview to select subtitle regions</li>
            <li>Use "Dual Zone" mode to select two separate regions</li>
            <li>Enable "Use Full Frame" to process the entire video frame</li>
            <li>Use arrow keys to navigate frames (Left/Right)</li>
          </ul>
          
          <p><strong>Settings:</strong></p>
          <ul>
            <li>Choose the appropriate language for OCR</li>
            <li>Adjust confidence and similarity thresholds for better accuracy</li>
            <li>Set time ranges to process only specific parts of the video</li>
            <li>Enable GPU acceleration if available</li>
          </ul>
          
          <p><strong>Output:</strong></p>
          <ul>
            <li>The generated SRT file will contain timestamped subtitles</li>
            <li>Progress will be shown in real-time during processing</li>
            <li>Notifications can be enabled for completion alerts</li>
          </ul>
        </div>
      )
    });
  };

  return (
    <div style={{ padding: '16px' }}>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Card title="Video Processing" size="small">
            <Space direction="vertical" style={{ width: '100%' }} size="middle">
              {/* Video File Selection */}
              <Row gutter={[8, 8]} align="middle">
                <Col span={4}>
                  <Text strong>Video File:</Text>
                </Col>
                <Col span={16}>
                  <div className={`drag-drop-area ${isDragging ? 'dragging' : ''}`}>
                    <Input
                      value={videoPath}
                      placeholder="Select a video file or drag & drop here..."
                      readOnly
                      style={{
                        backgroundColor: '#262626',
                        width: '100%'
                      }}
                      onDragOver={handleDragOver}
                      onDragLeave={handleDragLeave}
                      onDrop={handleDrop}
                    />
                  </div>
                </Col>
                <Col span={4}>
                  <Button
                    icon={<FolderOpenOutlined />}
                    onClick={handleSelectVideo}
                    block
                  >
                    Browse
                  </Button>
                </Col>
              </Row>

              {/* Output File Selection */}
              <Row gutter={[8, 8]} align="middle">
                <Col span={4}>
                  <Text strong>Output SRT:</Text>
                </Col>
                <Col span={16}>
                  <Input
                    value={outputPath}
                    placeholder="Output subtitle file path..."
                    readOnly
                    style={{ backgroundColor: '#262626' }}
                  />
                </Col>
                <Col span={4}>
                  <Button
                    icon={<SaveOutlined />}
                    onClick={handleSelectOutput}
                    disabled={!videoPath}
                    block
                  >
                    Save As
                  </Button>
                </Col>
              </Row>

              {/* Language and Position */}
              <Row gutter={[8, 8]} align="middle">
                <Col span={4}>
                  <Text strong>Language:</Text>
                </Col>
                <Col span={8}>
                  <select
                    value={settings.language}
                    onChange={(e) => updateSettings({ language: e.target.value })}
                    style={{
                      width: '100%',
                      padding: '6px 12px',
                      backgroundColor: '#262626',
                      border: '1px solid #434343',
                      borderRadius: '4px',
                      color: '#fff'
                    }}
                  >
                    <option value="">Select language</option>
                    {LANGUAGES.map(lang => (
                      <option key={lang.code} value={lang.code}>
                        {lang.name}
                      </option>
                    ))}
                  </select>
                </Col>
                <Col span={4}>
                  <Text strong>Position:</Text>
                </Col>
                <Col span={6}>
                  <select
                    value={settings.subtitlePosition}
                    onChange={(e) => updateSettings({ subtitlePosition: e.target.value })}
                    style={{
                      width: '100%',
                      padding: '6px 12px',
                      backgroundColor: '#262626',
                      border: '1px solid #434343',
                      borderRadius: '4px',
                      color: '#fff'
                    }}
                  >
                    <option value="">Select position</option>
                    <option value="center">Center</option>
                    <option value="left">Left</option>
                    <option value="right">Right</option>
                    <option value="any">Any</option>
                  </select>
                </Col>
                <Col span={2}>
                  <Button
                    icon={<QuestionCircleOutlined />}
                    onClick={showHelp}
                    type="text"
                  />
                </Col>
              </Row>


            </Space>
          </Card>
        </Col>

        {/* Video Player */}
        <Col span={24}>
          <Card title={`Video Preview ${videoPath ? `- ${videoPath.split(/[/\\]/).pop()}` : ''}`} size="small">
            {videoPath ? (
              <div>
                <Text type="secondary" style={{ fontSize: '12px', display: 'block', marginBottom: '8px' }}>
                  Path: {videoPath}
                </Text>
                {/* <Button
                  size="small"
                  onClick={() => {
                    // Force set test metadata
                    const testMetadata = {
                      path: videoPath,
                      width: 1920,
                      height: 1080,
                      totalFrames: 1000,
                      fps: 30,
                      duration: 33.33
                    };
                    console.log('🧪 Force setting test metadata');
                    // Get store directly
                    const store = useAppStore.getState();
                    store.setVideoMetadata(testMetadata);
                  }}
                  style={{ marginBottom: '8px' }}
                >
                  🧪 Force Load Test
                </Button> */}
                <VideoPlayer videoPath={videoPath} />
              </div>
            ) : (
              <div style={{
                height: 400,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#000',
                border: '1px solid #434343'
              }}>
                <Text type="secondary">Select a video file to preview</Text>
              </div>
            )}
          </Card>
        </Col>

        {/* Control Buttons */}
        <Col span={24}>
          <Card size="small">
            <Space>
              <Button
                type="primary"
                icon={<PlayCircleOutlined />}
                onClick={handleStartOCR}
                disabled={!videoPath || !outputPath || processStatus.isRunning}
                size="large"
              >
                Run OCR
              </Button>
              
              <Button
                danger
                icon={<StopOutlined />}
                onClick={handleCancelOCR}
                disabled={!processStatus.canCancel}
                size="large"
              >
                Cancel
              </Button>
            </Space>
          </Card>
        </Col>

        {/* Progress Output */}
        <Col span={24}>
          <ProgressOutput />
        </Col>
      </Row>
    </div>
  );
};

export default VideoTab;
